<template>
  <div>
    <v-dialog
      v-model="showLocalGameIntroDialogStatus"
      :fullscreen="$vuetify.breakpoint.xsOnly"
      :width="dialogWidth"
      scrollable
    >
      <v-card class="dialog-fill rounded-0">
        <div class="dialog-container-grad game-intro-grade-bg"></div>
        <v-card-title
          class="dialog-title py-0"
          :class="{ 'px-6': $vuetify.breakpoint.mdAndUp, 'px-4': $vuetify.breakpoint.smAndDown }"
        >
          <v-row no-gutters class="py-3">
            <div class="text-h6 grey-1--text custom-text-noto">{{ gameName }}</div>
            <v-spacer></v-spacer>
            <v-btn icon @click="showLocalGameIntroDialogStatus = false">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-row>
        </v-card-title>
        <v-card-text
          class="dialog-container"
          :class="{ 'px-6': $vuetify.breakpoint.mdAndUp, 'px-4': $vuetify.breakpoint.smAndDown }"
        >
          <div :class="{ 'd-flex': $vuetify.breakpoint.smAndUp }">
            <div
              :class="{
                'dialog-sticky-container mr-6': $vuetify.breakpoint.smAndUp,
                'd-flex flex-column justify-center align-center': $vuetify.breakpoint.xsOnly
              }"
            >
              <v-img
                :lazy-src="gameDefaultJpg"
                :src="gameCover"
                aspect-ratio="1"
                :placeholder="gameDefaultJpg"
                @error="setAltImg()"
                style="
                  border-top-left-radius: 12px;
                  border-top-right-radius: 12px;
                  border-bottom-right-radius: 12px;
                "
                :style="{ width: coverWidth }"
              >
                <div class="d-flex justify-center align-end h-100-percent pb-2 px-2">
                  <span class="default-content--text trade-text-type text-no-wrap">
                    {{ brandNameDisplay }}
                  </span>
                </div>
                <div
                  v-if="isGameMaintaining"
                  class="d-flex v-card--reveal gradient-game-maintenance h-100-percent"
                >
                  <span
                    class="material-symbols-outlined default-content--text material-icons md-48"
                  >
                    construction
                  </span>
                </div>
              </v-img>
              <v-row
                no-gutters
                :class="{
                  'd-flex mt-4': $vuetify.breakpoint.mdAndUp,
                  'd-flex flex-column mt-4 gap-8': $vuetify.breakpoint.smOnly,
                  'd-flex flex-row-reverse justify-center my-4 w-100': $vuetify.breakpoint.xsOnly
                }"
              >
                <v-col
                  :cols="startsPlayBtnCol"
                  v-if="isLogin"
                  :class="{
                    'pr-2': isLogin && $vuetify.breakpoint.mdAndUp && startsPlayBtnCol !== 12,
                    'pl-2': isLogin && $vuetify.breakpoint.xsOnly && startsPlayBtnCol !== 12,
                    'mb-2':
                      startsPlayBtnCol === 12 &&
                      freePlayBtnCol === 12 &&
                      $vuetify.breakpoint.mdAndUp
                  }"
                >
                  <v-btn
                    depressed
                    rounded
                    color="gradient-button"
                    class="button-content--text"
                    :disabled="disabledStatus || isVipLevelLimit || isGameMaintaining"
                    :small="$vuetify.breakpoint.smAndDown"
                    @click="startGameWithClick('play')"
                    block
                  >
                    <v-icon left v-if="isVipLevelLimit"> mdi-lock-outline </v-icon>
                    {{ $t('startsPlay') }}
                  </v-btn>
                </v-col>
                <v-col cols="12" v-else-if="!hasDemo">
                  <v-btn
                    depressed
                    rounded
                    color="gradient-button"
                    class="button-content--text w-100"
                    :disabled="disabledStatus || isGameMaintaining"
                    :small="$vuetify.breakpoint.smAndDown"
                    @click="loginClickHandler"
                    block
                  >
                    {{ $t('startsPlay') }}
                  </v-btn>
                </v-col>
                <v-col
                  :cols="freePlayBtnCol"
                  v-if="hasDemo"
                  :class="{
                    'pl-2': isLogin && $vuetify.breakpoint.mdAndUp && freePlayBtnCol !== 12,
                    'pr-2': isLogin && $vuetify.breakpoint.xsOnly && freePlayBtnCol !== 12
                  }"
                >
                  <v-btn
                    outlined
                    rounded
                    color="white"
                    class="default-content--text"
                    :disabled="disabledStatus || isGameMaintaining"
                    :small="$vuetify.breakpoint.smAndDown"
                    @click="startGameWithClick('demo')"
                    block
                  >
                    {{ $t('freePlay') }}
                  </v-btn>
                </v-col>
              </v-row>
            </div>
            <div class="d-flex flex-column game-notices">
              <div v-if="showGameNotice" class="d-flex flex-column gap-8">
                <div>
                  <span class="text-subtitle-1 default-content--text custom-text-noto">
                    {{ $t('game_notice') }}
                  </span>
                </div>
                <div v-if="isLive">
                  <span class="text-caption default-content--text custom-text-noto">{{
                    $t('game_live_dialog_noty2', { brandName: $t('xincity') })
                  }}</span>
                </div>
                <div v-if="!hasExp && hasRobot">
                  <span class="text-caption primary--text custom-text-noto"
                    >*{{ $t('game_no_experience_and_play_with_robot') }}</span
                  >
                </div>
                <div v-else-if="!hasExp">
                  <span class="text-caption primary--text custom-text-noto"
                    >*{{ $t('game_no_experience_accumulation_notice') }}</span
                  >
                </div>
                <div v-else-if="hasRobot">
                  <span class="text-caption primary--text custom-text-noto"
                    >*{{ $t('play_game_with_robot') }}</span
                  >
                </div>
              </div>
              <div v-if="showVipLevelLimit" class="game-intro-grade-border pa-4 w-100">
                <div class="d-flex justify-space-between align-center">
                  <div>
                    <div class="d-flex align-center">
                      <span
                        class="material-symbols-outlined gradient-primary--text material-icons md-20"
                      >
                        lock
                      </span>
                      <span class="text-subtitle-1 gradient-primary--text ml-1 custom-text-noto">
                        {{ $t('game_rank_limit') }}
                      </span>
                    </div>
                    <div>
                      <span class="text-caption default-content--text custom-text-noto">
                        {{ $t('game_rank_limit_reached', { vipLevelText: gameVipLevelText }) }}
                      </span>
                    </div>
                  </div>
                  <div v-if="game.vipLevel">
                    <vipLevelIcon
                      width="48"
                      height="48"
                      :vip-level="gameRequireVipLevel"
                    ></vipLevelIcon>
                  </div>
                </div>
              </div>
              <div v-if="(showGameNotice || showVipLevelLimit) && showRtp">
                <v-divider></v-divider>
              </div>
              <div v-if="showRtp">
                <div class="text-subtitle-1 default-content--text mb-2 custom-text-noto">
                  <span>
                    {{ $t('real_time_rtp') }}
                  </span>
                </div>
                <div>
                  <rtpDashboad
                    :daily-rtp="gameRtp.dailyRtp"
                    :weekly-rtp="gameRtp.weeklyRtp"
                    :monthly-rtp="gameRtp.monthlyRtp"
                    :rtp-style-obj="showRTPStyle"
                    :default-rtp="defaultRtp"
                    :no-experience-text="!hasExp"
                    :maintain-text="isGameMaintaining"
                  />
                </div>
              </div>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <notyNotRealMember
      v-if="showNotyNotRealMemberDialogStatus"
      :show-noty-not-real-member-dialog-status.sync="showNotyNotRealMemberDialogStatus"
    />
    <bothRobotExpNoty
      v-if="showNotyBothRobotExpNotyDialogStatus.show"
      :show-noty-both-robot-exp-noty-dialog-status.sync="showNotyBothRobotExpNotyDialogStatus"
    />
    <noExpGainNoty
      v-if="showNotyNoExpGainNotyDialogStatus.show"
      :show-noty-no-exp-gain-noty-dialog-status.sync="showNotyNoExpGainNotyDialogStatus"
    />
    <hasRobotNoty
      v-if="showNotyHasRobotNotyDialogStatus.show"
      :show-noty-has-robot-noty-dialog-status.sync="showNotyHasRobotNotyDialogStatus"
    />
  </div>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import gameStatus from '@/mixins/gameStatus.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import gameRtpWatcher from '@/mixins/gameRtpWatcher.js'
  const STATION = process.env.STATION
  const playGame = require(`~/mixins_station/${STATION}/playGame`).default
  export default {
    name: 'gameIntro',
    mixins: [analytics, preLoginAction, playGame, gameStatus, gameRtpWatcher],
    components: {
      rtpDashboad: () => import(`~/components_station/${STATION}/rtp/rtpDashboad.vue`),
      bothRobotExpNoty: () => import('~/components/notifications/bothRobotExpNoty.vue'),
      notyNotRealMember: () => import('~/components/notifications/notyNotRealMember.vue'),
      noExpGainNoty: () => import('~/components/notifications/noExpGainNoty.vue'),
      hasRobotNoty: () => import('~/components/notifications/hasRobotNoty.vue'),
      vipLevelIcon: () => import('~/components/player_info/vipLevelIcon')
    },
    props: {
      game: {
        type: Object,
        default: () => {}
      },
      showGameIntroDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        disabledStatus: false,
        gameDefaultJpg: this.$store.getters['gameHall/gameDefaultImgJpg'],
        showNotyNotRealMemberDialogStatus: false
      }
    },
    watch: {
      openGameLock: {
        handler(val) {
          this.disabledStatus = val
        }
      }
    },
    computed: {
      showLocalGameIntroDialogStatus: {
        get() {
          return this.showGameIntroDialogStatus
        },
        set(val) {
          this.$emit('update:showGameIntroDialogStatus', val)
        }
      },
      dialogWidth() {
        return this.$vuetify.breakpoint.mdAndUp ? '720px' : '552px'
      },
      coverWidth() {
        return this.$vuetify.breakpoint.mdAndUp
          ? this.$UIConfig.gameIntro.coverWidth.mdAndUp
          : this.$UIConfig.gameIntro.coverWidth.smAndDown
      },
      breakpoints() {
        return {
          mdAndUp: this.$vuetify.breakpoint.mdAndUp,
          smOnly: this.$vuetify.breakpoint.smOnly,
          xsOnly: this.$vuetify.breakpoint.xsOnly
        }
      },
      isEn() {
        return this.$i18n.locale === 'en'
      },
      btnTextOverFlow() {
        const sLen = this.$t('startsPlay').length
        const fLen = this.$t('freePlay').length
        return sLen >= 5 || fLen >= 5
      },
      startsPlayBtnCol() {
        const { mdAndUp, smOnly, xsOnly } = this.breakpoints

        if (!this.hasDemo) {
          return 12
        } else if (this.isLogin) {
          switch (true) {
            case mdAndUp:
              if (this.isEn && this.btnTextOverFlow) {
                return 12
              } else {
                return 6
              }
            case smOnly:
              return 12
            case xsOnly:
              return 6
          }
        }
      },
      freePlayBtnCol() {
        const { mdAndUp, smOnly, xsOnly } = this.breakpoints

        switch (true) {
          case !this.isLogin && xsOnly:
            return 12
          case !this.isLogin:
            return 12
          case mdAndUp:
            if (this.isEn && this.btnTextOverFlow) {
              return 12
            } else {
              return 6
            }
          case smOnly:
            return 12
          case xsOnly:
            return 6
        }
      },
      openGameLock({ $store }) {
        return $store.getters['gameHall/openGameLock']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      brandName({ $store }) {
        const providers = $store.getters['gameProvider/providers']
        const providerBrandName = providers.find((item) => {
          return item.id === this.game.platformId
        })
        return providerBrandName?.brand
      },
      brandNameDisplay() {
        return this.brandName?.toUpperCase()
      },
      gameName() {
        const { name, title } = this.game
        return name || title
      },
      gameCover() {
        const { thumbUrl, gameCover } = this.game
        return thumbUrl || gameCover
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },

      showGameNotice() {
        return this.isLive || !this.hasExp || this.hasRobot
      },

      showVipLevelLimit() {
        return !this.isLogin || this.isVipLevelLimit || this.informalMember
      },
      isVipLevelLimit() {
        return (
          !this.informalMember &&
          ((this.game.vipLevel === -1 && this.informalMember) ||
            (this.game.vipLevel !== 99 && this.vipLevel < this.game.vipLevel))
        )
      },
      informalMember() {
        return this.vipLevel === 0
      },
      gameRequireVipLevel() {
        return this.game.vipLevel === -1 ? 1 : this.game.vipLevel
      },
      gameVipLevelText() {
        const vipLevelTitles = this.$store.getters['role/vipLevelTitle']
        return this.$t(vipLevelTitles[this.gameRequireVipLevel])
      },
      // 新增：從 RTP Map 獲取 RTP 資料
      gameRtp() {
        // 使用 gameStatus mixin 的 isLive computed property
        if (this.isLive) {
          return null
        }
        return this.getGameRtp(this.game.id)
      }
    },
    methods: {
      async setAltImg() {
        if (this.gameCover.includes('default')) {
          return
        }

        this.gameCover = this.gameCover.replace('.webp', '.jpg')

        try {
          const imageResponse = await fetch(this.gameCover)
          if (!imageResponse.ok) {
            this.gameCover = this.gameDefaultJpg
          }
        } catch (error) {
          this.gameCover = this.gameDefaultJpg
        }
      },
      loginClickHandler() {
        this.startGameWithClick('play')
        this.setPreLoginAction('playGame', () => this.startGameWithClick('play'))
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '~vuetify/src/styles/settings/_variables.scss';
  $sm: map-get($display-breakpoints, 'sm-and-up');
  .dialog-container-grad {
    z-index: 2;
    position: absolute;
    height: 180px;
    width: 100%;
  }
  .dialog-container {
    z-index: 3;
    position: relative;
    padding: 0;
    height: 100%;
  }
  .dialog-title {
    z-index: 3;
    position: relative;
  }
  .dialog-sticky-container {
    position: sticky;
    top: 0px;
    height: fit-content;
  }
  .trade-text-type {
    text-align: center;

    /* Customize/caption */
    font-family: 'Montserrat';
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: uppercase;
  }
  .game-intro-grade-border {
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  ::v-deep .v-dialog {
    @media #{$sm} {
      border-radius: 8px;
    }
  }
  .gap-8 {
    gap: 8px;
  }
  .gap-16 {
    gap: 16px;
  }
  .game-notices {
    flex: 1;
    gap: 24px;
  }
</style>
