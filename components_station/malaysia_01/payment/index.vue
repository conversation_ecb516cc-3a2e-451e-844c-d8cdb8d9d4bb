<template>
  <div>
    <v-dialog
      v-model="showPaymentDialogStatusTmp"
      :fullscreen="breakpoint.xsOnly"
      max-width="620"
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card elevation="0" tile color="dialog-fill" id="malaysiaPaymentDialog">
        <customDialogTitle
          :title="
            $UIConfig.paymentIndex.title.format(
              $t('points').toUpperCase(),
              $t('stored_mall').toUpperCase()
            )
          "
          @closeDialog="closeDialog"
          :disabled="disableBtn"
        />
        <div :class="breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm'">
          <!-- 標籤 -->
          <v-tabs
            ref="tabs"
            background-color="transparent"
            color="primary"
            class="px-4 px-sm-6"
            show-arrows
            v-model="selectType"
          >
            <v-tab
              href="#yoeGame"
              class="a-link"
              key="yoeGame"
              :disabled="disableBtn"
              v-if="$UIConfig.paymentIndex.showGiftPack && showShoppingMall"
            >
              {{ $t('yoeGame') }}
            </v-tab>
            <v-tab href="#coffer" class="a-link" key="coffer" :disabled="disableBtn">
              {{ $t('coffer') }}
            </v-tab>
          </v-tabs>
          <!-- 內容 -->
          <v-tabs-items v-model="selectType" :class="{ 'pb-6': selectType === 'yoeGame' }">
            <!-- 商城 -->
            <v-tab-item key="yoeGame" value="yoeGame" v-if="$UIConfig.paymentIndex.showGiftPack">
              <v-container fluid class="py-0 px-4">
                <template v-if="yoeShopArray && !maintainSystem[0].maintaining">
                  <span
                    class="primary--text custom-text-noto gift-pack-remark py-3 px-2 text-medium--text"
                    >{{ $t('no_refund') }}</span
                  >
                  <giftPackArea :provider-list="yoeShopArray" />
                </template>
                <div v-else class="no-purchase-items custom-text-noto pa-6">
                  <span class="custom-text-noto gift-pack-remark text-medium--text">{{
                    $t('no_refund')
                  }}</span>
                  <div class="mt-6">
                    <p class="mb-1">{{ $t('no_purchase_items') }}</p>
                    <span>{{ $t('contact_support') }}</span>
                  </div>
                </div>
              </v-container>
            </v-tab-item>
            <!-- 保險箱 -->
            <v-tab-item key="coffer" value="coffer">
              <!-- 固定最小高度，切換tab時才不會因為v-if而跳一下 -->
              <div style="min-height: 360px">
                <!-- 加v-if是為了讓data重設 -->
                <coffer v-if="selectType == 'coffer'" @setDisableBtn="setDisableBtn" />
              </div>
            </v-tab-item>
          </v-tabs-items>
        </div>
      </v-card>
      <toBeFormalDialog
        v-if="showTobeFormalDialogStatus"
        @goToGiftPack="goToGiftPack"
        :show-tobe-formal-dialog-status.sync="showTobeFormalDialogStatus"
      />
    </v-dialog>
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import utilWhiteList from '@/utils/whiteList.js'
  const STATION = process.env.STATION
  export default {
    name: 'PaymentIndexDialog',
    mixins: [hiddenScrollHtml, analytics, scssLoader],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      giftPackArea: () => import(`~/components_station/${STATION}/payment/giftPackArea`),
      toBeFormalDialog: () => import('~/components/payment/toBeFormalDialog'),
      coffer: () => import(`~/components_station/${STATION}/payment/coffer`)
    },
    props: {
      showPaymentDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        showPaymentDialogStatusTmp: this.showPaymentDialogStatus,
        showTable: true,
        selectType: '',
        //非正式會員使用保險箱會跳出叫你成為正式會員的dialog
        showTobeFormalDialogStatus: false,
        //當load時不應該可以做其他動作，故將按鈕disable
        disableBtn: false
      }
    },
    async created() {
      this.selectType = this.showShoppingMall ? 'yoeGame' : 'coffer'
      await this.$store.dispatch('role/updateUserDetail')
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      yoeShopArray({ $store }) {
        return $store.getters['yoeShop/yoeShopArray']
      },
      showShoppingMall({ $store }) {
        if (!process.client) return true
        const url = window.location.origin
        const userName = $store.getters['role/userName']
        return utilWhiteList.showShoppingMall(userName, url)
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      selectType: {
        async handler(newType, oldType) {
          if (newType === 'coffer') {
            if (this.vipLevel === 0) {
              //無解 目前只能用setTimeout強制讓tab跳回去
              //使用nextTick會有畫面閃爍的問題
              //不使用的話tab會直接跑到保險箱
              setTimeout(() => {
                this.selectType = oldType
              }, 1)
              this.showTobeFormalDialogStatus = true
            }
          }
        }
      }
    },
    async mounted() {
      await this.$store.dispatch('yoeShop/fetchYoeGame')
    },
    methods: {
      closeDialog() {
        this.showPaymentDialogStatusTmp = false
        this.$emit('update:showPaymentDialogStatus', false)
      },
      goToGiftPack() {
        this.selectType = 'yoeGame'
      },
      setDisableBtn(state) {
        this.disableBtn = state
      }
    },
    beforeDestroy() {
      this.showPaymentDialogStatusTmp = false
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $tab-focused: map-get($colors, 'tab-focused');
  .border-style-title {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-top: solid 1px $primary-variant-3 !important;
  }
  .border-style-bottom {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-bottom: solid 1px $primary-variant-3 !important;
  }

  .v-tabs-items {
    background-color: transparent !important;
  }
  .a-link {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .v-tab--active {
    color: $tab-focused !important;
  }
  .gift-pack-remark {
    display: block !important;
    font-size: 14px !important;
    width: 100% !important;
    white-space: pre-wrap !important;
    text-align: left !important;
  }
  .no-purchase-items span {
    font-size: 12px;
    color: #ffffff4d;
  }
  .yoeGame-full-page {
    max-height: calc(100vh - 126px) !important;
    overflow-y: scroll !important;
  }
  .yoeGame-page {
    max-height: calc(90vh - 124px) !important;
    overflow-y: scroll !important;
  }
  #malaysiaPaymentDialog {
    .scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    .scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      .scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      .scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
</style>
