<template>
  <v-row no-gutters class="pack-content">
    <v-col
      :cols="$vuetify.breakpoint.width >= 520 ? 4 : 6"
      v-for="(info, index) in filteredProviderList"
      :key="index"
      class="pa-1 pa-sm-2"
    >
      <yoeProviderCard :provider-info="info" :background-style="giftPackCardBg" />
    </v-col>
  </v-row>
</template>

<script>
  const STATION = process.env.STATION
  export default {
    name: 'providerCardListContent',
    components: {
      yoeProviderCard: () => import(`~/components_station/${STATION}/payment/yoeProviderCard`)
    },
    props: {
      providerList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        expiredProviderIds: [] // 儲存已過期的 provider id
      }
    },
    computed: {
      filteredProviderList() {
        return (
          this.providerList
            .filter((provider) => {
              return provider.isBigCard && !this.expiredProviderIds.includes(provider.id)
            })
            // 先過濾出大卡且未過期的再進行排序
            .sort((a, b) => {
              const orderA = typeof a.sortOrder === 'number' ? a.sortOrder : Infinity
              const orderB = typeof b.sortOrder === 'number' ? b.sortOrder : Infinity
              return orderA - orderB
            })
        )
      },
      swiperSlideStyle() {
        const breakWidth = this.$vuetify.breakpoint.width
        const width =
          breakWidth >= 1264
            ? this.$UIConfig.swiperBox.giftPackCardWidth.lg
            : breakWidth >= 960
            ? this.$UIConfig.swiperBox.giftPackCardWidth.md
            : breakWidth >= 600
            ? this.$UIConfig.swiperBox.giftPackCardWidth.sm
            : breakWidth >= 520
            ? this.$UIConfig.swiperBox.giftPackCardWidth.xs
            : this.$UIConfig.swiperBox.giftPackCardWidth.twoGiftPack
        return { boxSizing: 'border-box', width }
      },
      giftPackCardBg() {
        return {
          bigCard: this.$UIConfig.swiperBox.giftPackCardBg.bigCard,
          bigVip: this.$UIConfig.swiperBox.giftPackCardBg.bigVip
        }
      }
    },
    methods: {}
  }
</script>

<style lang="scss" scoped>
  .swiper-container::v-deep {
    overflow: visible;
    .swiper-item {
      overflow-y: visible !important;
      overflow-x: hidden;
      padding-top: 8px;
    }
  }
</style>
