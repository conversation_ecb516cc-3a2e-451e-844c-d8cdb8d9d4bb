<template>
  <div>
    <providerCardListContent :provider-list="yoeShopArray" />

    <!--取消-->
    <yoeConfirmDialog v-model="showCancelDialog" :show-cancel="false">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{ $t('cancelYoeShopNoty') }}</span>
    </yoeConfirmDialog>
    <!--成功-->
    <yoeConfirmDialog v-model="showSuccessDialog" :show-cancel="false">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <div class="d-flex align-center">
        <span class="default-content--text">{{ $t('yoeShopSuccessNoty') }}</span>
      </div>
    </yoeConfirmDialog>
    <!--成功但沒收到訂單-->
    <yoeConfirmDialog v-model="showPurchaseCancelDialog" :show-cancel="false">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <span class="default-content--text">{{
        $t('successButPurchaseFalseNoty').split('\n')[0]
      }}</span
      ><br />
      <span class="default-content--text">
        {{ $t('successButPurchaseFalseNoty').split('\n')[1] }}
      </span>
    </yoeConfirmDialog>
  </div>
</template>

<script>
  const STATION = process.env.STATION

  export default {
    name: 'giftPackArea',
    components: {
      providerCardListContent: () =>
        import(`~/components_station/${STATION}/payment/providerCardListContent`),
      yoeConfirmDialog: () => import('~/components/payment/yoeConfirmDialog')
    },
    data() {
      return {
        showCancelDialog: false,
        showSuccessDialog: false,
        showPurchaseCancelDialog: false
      }
    },
    computed: {
      yoeShopArray({ $store }) {
        return $store.getters['yoeShop/yoeShopArray']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      }
    },
    mounted() {
      this.$nuxt.$on('yoe:dialogStatusChange', this.handleYoeDialogStatusChange)
      this.$nuxt.$on('yoe:showYoeDialogStatus', this.handleYoeDialogStatusChange)
    },
    beforeDestroy() {
      this.$nuxt.$off('yoe:dialogStatusChange', this.handleYoeDialogStatusChange)
      this.$nuxt.$off('yoe:showYoeDialogStatus', this.handleYoeDialogStatusChange)
    },
    methods: {
      handleConfirm() {
        this.$store.commit('yoeShop/SET_PRODUCT_INFO', this.providerInfo)
        this.$nuxt.$emit('root:showYoeDialogStatus', {
          show: true,
          shopItem: this.providerInfo,
          cancel: false,
          purChaseCancel: false,
          onClose: async () => {
            // 對話框關閉時重新獲取數據
            await this.$store.dispatch('yoeShop/fetchYoeGame')
          }
        })
      },
      async handleYoeDialogStatusChange(value) {
        // 維持原有的狀態處理邏輯
        if (value.cancel) {
          this.showCancelDialog = true
        }
        if (!value.cancel) {
          await this.$store.dispatch('yoeShop/fetchYoeGame')
          this.showSuccessDialog = true
        }
        if (!value.cancel && value.purChaseCancel) {
          this.showPurchaseCancelDialog = true
        }
      }
    }
  }
</script>
