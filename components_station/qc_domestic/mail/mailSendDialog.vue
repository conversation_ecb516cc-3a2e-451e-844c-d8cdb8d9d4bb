<!-- eslint-disable vue/no-v-html -->
<template>
  <div>
    <v-dialog
      v-model="showMailSendDialogStatusTmp"
      :fullscreen="breakpoint.xsOnly"
      :width="breakpoint.xsOnly ? '375px' : '457px'"
      scrollable
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card color="transparent">
        <customDialogTitle
          :title="$t('send_mail').toUpperCase()"
          @closeDialog="
            () =>
              (selectedFriend && selectedFriend.length != 0) ||
              (mailForm.mailContent && mailForm.mailContent.length != 0) ||
              (mailForm.xinCoin && mailForm.xinCoin.length != 0)
                ? (confirmLeaveStatus = true)
                : closeDialog()
          "
        />
        <v-card-text class="pb-0 px-4 pt-4 px-sm-6 pt-sm-6">
          <v-row no-gutters class="fill-height">
            <v-form ref="form" style="width: 100%">
              <v-card class="d-flex flex-column" color="transparent" flat>
                <v-autocomplete
                  ref="mailRecipient"
                  class="rounded-xxl rounded-b-0"
                  :class="{ 'custom-autocomplete': !stringNullOrEmpty(selectedFriend) }"
                  v-model="selectedFriend"
                  :search-input.sync="searchInput"
                  v-validate="'required'"
                  data-vv-name="recipient"
                  :data-vv-as="$t('recipient')"
                  data-vv-scope="mail"
                  :error-messages="errors.first('mail.recipient')"
                  :label="$t('recipient') + '*'"
                  :items="recipientList"
                  item-text="username"
                  item-value="username"
                  filled
                  chips
                  :disabled="recipientDisabledStatus"
                  clearable
                  clear-icon="mdi-close-circle"
                  @click:clear="clearRecipientField"
                >
                  <template v-slot:append-outer v-if="canSearchStranger">
                    <div>
                      <v-icon
                        :disabled="isRecipientSelf"
                        @mouseup.stop
                        @click.prevent.stop="searchPlayer"
                        >mdi-magnify</v-icon
                      >
                    </div>
                  </template>
                  <template v-slot:selection="data">
                    <v-menu ref="playerInfoMenu" z-index="210">
                      <template v-slot:activator="{ on }">
                        <v-chip
                          id="recipient-chip"
                          v-bind="data.attrs"
                          v-on="on"
                          :input-value="data.selected"
                          color="grey-3"
                          class="default-content--text"
                          @click="setPlayerInfo(data.item.username)"
                        >
                          <v-avatar v-if="selectedFriend === userName" left>
                            <v-img
                              :src="getImage('mahjongstarlogo.png')"
                              @error="errorImgHandler(data.item)"
                            >
                              <template v-slot:placeholder>
                                <v-row class="fill-height ma-0" align="center" justify="center">
                                  <v-img :src="defaultImg" contain />
                                </v-row>
                              </template>
                            </v-img>
                          </v-avatar>
                          <v-avatar v-else left>
                            <v-img :src="data.item.thumbUrl" @error="errorImgHandler(data.item)">
                              <template v-slot:placeholder>
                                <v-row class="fill-height ma-0" align="center" justify="center">
                                  <v-img :src="defaultImg" contain />
                                </v-row>
                              </template>
                            </v-img>
                          </v-avatar>

                          {{ data.item.username }}
                        </v-chip>
                      </template>
                    </v-menu>
                  </template>
                  <template v-slot:no-data>
                    <v-list-item class="px-0">
                      <v-list-item-content class="custom-text-noto text-body-2 grey-2--text pl-4">
                        {{
                          isRecipientSelf
                            ? $t('cant_send_email_to_self')
                            : canSearchStranger
                            ? $t('friend_not_found_msg')
                            : $t('mail_send_text2')
                        }}
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                  <template v-slot:item="data">
                    <template v-if="data.item.noty">
                      <span class="custom-text-noto text-body-2 grey-2--text">
                        {{ data.item.text }}
                      </span>
                    </template>
                    <template v-else>
                      <v-row no-gutters style="margin-left: -16px" class="sendMailMenu">
                        <v-list-item-avatar v-if="data.item.otherGame" class="ml-4">
                          <v-img :src="data.item.thumbUrl" contain>
                            <template v-slot:placeholder>
                              <v-row class="fill-height ma-0" align="center" justify="center">
                                <v-img :src="getImage('mahjongstarlogo.png')" contain />
                              </v-row>
                            </template>
                          </v-img>
                        </v-list-item-avatar>
                        <v-badge
                          v-else
                          bottom
                          :color="data.item.online == 1 ? 'green' : 'grey-2'"
                          dot
                          bordered
                          offset-x="25"
                          offset-y="22"
                        >
                          <v-list-item-avatar>
                            <v-img :src="data.item.thumbUrl" contain>
                              <template v-slot:placeholder>
                                <v-row class="fill-height ma-0" align="center" justify="center">
                                  <v-img :src="defaultImg" contain />
                                </v-row>
                              </template>
                            </v-img>
                          </v-list-item-avatar>
                        </v-badge>
                        <v-list-item-content>
                          <!-- 角色名稱 -->
                          <v-list-item-title
                            class="custom-text-noto text-subtitle-2"
                            :class="
                              data.item.online == 1 || data.item.otherGame
                                ? 'default-content--text'
                                : 'btn-disable--text'
                            "
                            v-text="data.item.username"
                          />
                          <!-- 其他遊戲角色 subtitle -->
                          <v-list-item-subtitle
                            v-if="data.item.otherGame"
                            class="custom-text-noto text-body-2 default-content--text"
                          >
                            {{ data.item.subTitle }}
                          </v-list-item-subtitle>
                          <!-- 好友 subtitle -->
                          <v-list-item-subtitle
                            v-else
                            class="custom-text-noto text-body-2"
                            :class="{
                              'default-content--text': data.item.online == 1,
                              'btn-disable--text': !data.item.online == 1
                            }"
                          >
                            LV {{ data.item.online ? data.item.level : '-' }}
                          </v-list-item-subtitle>
                        </v-list-item-content>
                      </v-row>
                      <div v-if="data.item.otherGame">
                        <div class="d-flex">
                          <v-img :src="getImage('coin3.png')" width="24" height="24" />
                          <div class="ml-2">{{ $t(data.item.gameName) }}</div>
                        </div>
                      </div>
                    </template>
                  </template>
                </v-autocomplete>
                <span
                  v-if="this.selectedFriend !== this.userName"
                  class="custom-text-noto text-body-2 grey-3--text pb-4"
                  >＊{{ canSearchStranger ? $t('recipient_noty') : $t('mail_send_text1') }}
                </span>
                <!-- 標題 -->
                <v-text-field
                  v-model="mailForm.mailTitle"
                  v-validate="'required'"
                  disabled
                  :error-messages="errors.first('mail_title')"
                  :label="$t('mail_title') + '*'"
                  :data-vv-as="$t('mail_title')"
                  data-vv-name="mail_title"
                  data-vv-scope="mail"
                  filled
                  shaped
                />
                <!-- 內容 -->
                <v-textarea
                  v-if="this.selectedFriend === this.userName"
                  v-model="mailForm.mailContent"
                  v-validate.continues="'allow_blank_required|min:1|max:120'"
                  :error-messages="errors.first('mail.mail_content')"
                  :label="$t('mail_content') + '*'"
                  data-vv-name="mail_content"
                  :data-vv-as="$t('mail_content')"
                  data-vv-scope="mail"
                  filled
                  shaped
                  height="125"
                  no-resize
                  :counter-value="() => count"
                  :disabled="selectedFriend === userName"
                >
                </v-textarea>
                <v-textarea
                  v-else
                  v-model="mailForm.mailContent"
                  v-validate.continues="'allow_blank_required|min:1|max:120'"
                  :error-messages="errors.first('mail.mail_content')"
                  :label="$t('mail_content') + '*'"
                  data-vv-name="mail_content"
                  :data-vv-as="$t('mail_content')"
                  data-vv-scope="mail"
                  filled
                  shaped
                  height="125"
                  no-resize
                  :maxlength="handleCurrentMaxLen"
                  counter="120"
                  :counter-value="() => count"
                  :disabled="selectedFriend === userName"
                >
                </v-textarea>
              </v-card>
              <v-divider />
              <!-- 附件 -->
              <v-card
                class="d-flex py-2 default-content--text align-center"
                color="transparent"
                flat
              >
                <span>
                  {{ $t('appendix') }}
                </span>
                <v-btn
                  color="transparent"
                  icon
                  plain
                  elevation="0"
                  @click="openAttachmentDescriptionDialog"
                >
                  <span class="material-symbols-rounded default-content--text"> help </span>
                </v-btn>
              </v-card>
              <!-- 星幣 -->
              <v-card class="d-flex" color="transparent" flat>
                <v-card color="transparent" class="pr-2 mt-4" flat>
                  <v-img
                    :src="getImage('<EMAIL>')"
                    :srcset="getSrcset('coin')"
                    width="24"
                    height="24"
                  />
                </v-card>
                <v-text-field
                  ref="xinCoinInput"
                  v-model="mailForm.xinCoin"
                  v-validate="'required|coin_min_send_mail:10000|coin_max:1000000000'"
                  type="text"
                  min="10000"
                  max="999999999"
                  :hint="feeMessage"
                  persistent-hint
                  oninput="if(value.length>5)value=value.slice(0,9)"
                  :error-messages="errors.first('mail.xin_coin')"
                  :label="$t('xin_coin') + '*'"
                  :data-vv-as="$t('xin_coin')"
                  data-vv-name="xin_coin"
                  data-vv-scope="mail"
                  filled
                  shaped
                  class="pa-0"
                  hide-spin-buttons
                  :placeholder="selectedFriend === userName ? $t('mail_xin_coin_placeholder') : ''"
                />
              </v-card>
            </v-form>
          </v-row>
        </v-card-text>
        <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
          <!-- 若是選擇的人是自己，則代表要轉幣給其他遊戲的角色 -->
          <template v-if="breakpoint.smAndUp">
            <v-spacer />
            <v-btn
              :disabled="!valid"
              color="primary-variant-1"
              class="button-content--text"
              depressed
              @click="prepareSendHandler"
            >
              {{ $t('send_out').toUpperCase() }}
            </v-btn>
          </template>
          <template v-else>
            <v-btn
              :disabled="!valid"
              color="primary-variant-1"
              class="button-content--text"
              depressed
              @click="prepareSendHandler"
              style="width: 100%"
            >
              {{ $t('send_out').toUpperCase() }}
            </v-btn>
          </template>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-if="confirmSendStatus"
      v-model="confirmSendStatus"
      persistent
      max-width="380"
      content-class="rounded-lg"
    >
      <v-card color="transparent" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
          {{ $t('reminder') }}
        </v-card-title>
        <v-card-text class="px-0 py-6">
          <v-row no-gutters>
            <span class="custom-text-noto text-body-2 default-content--text">
              {{ $t('dialog_before_send_noty') }}
              {{ selectedFriend }}
            </span></v-row
          >
          <v-row no-gutters class="custom-text-noto text-body-2 default-content--text">
            {{ formatPrice(parseInt(mailForm.xinCoin)) + ' ' + $t('xin_coin') }}
          </v-row>
          <v-row no-gutters class="custom-text-noto text-body-2 default-content--text">
            {{ $t('mail_send_fee_noty') }}
            {{ formatNumber(confirmDialogMailFee) + ' ' + $t('xin_coin') }}
          </v-row>
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
              <v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="confirmSendStatus = false"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                color="primary-variant-1"
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                depressed
                :disabled="confirmSendBtnStatus"
                @click="sendMailHandler"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-if="confirmLeaveStatus"
      v-model="confirmLeaveStatus"
      persistent
      max-width="380"
      content-class="rounded-lg"
    >
      <v-card color="transparent" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
          {{ $t('reminder') }}
        </v-card-title>
        <v-card-text class="custom-text-noto text-body-2 default-content--text px-0 py-6">
          {{ $t('leave_mail_description1') }}
        </v-card-text>

        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="confirmLeaveStatus = false"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                color="primary-variant-1"
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                depressed
                @click="closeDialog"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <sendStrangerNotyDialog
      v-if="sendStrangerNotyDialogStatus"
      :send-stranger-noty-dialog-status.sync="sendStrangerNotyDialogStatus"
      :player-info="playerInfo"
    />
    <xinAttachmentDescriptionDialog
      v-if="showAttachmentDescriptionStatus"
      :show-attachment-description-status.sync="showAttachmentDescriptionStatus"
    />
    <mjsAttachmentDescriptionDialog
      v-if="showMjsAttachmentDescriptionStatus"
      :show-attachment-description-status.sync="showMjsAttachmentDescriptionStatus"
    />
    <confirmToMjsDialog
      v-if="showConfirmToMjsDialogStatus"
      @closeDialog="closeDialog"
      :show-confirm-to-mjs-dialog-status.sync="showConfirmToMjsDialogStatus"
      :name="userName"
      :xin-coin="mailForm.xinCoin"
      :mjs-coin="mailForm.xinCoin"
    />
  </div>
</template>

<script>
  import { isNullOrEmpty } from '@/utils/stringUtils'
  import Decimal from 'decimal.js'
  import relationship from '@/mixins/relationship.js'
  import cloneDeep from 'lodash/cloneDeep'
  import images from '~/mixins/images'
  import scssLoader from '@/mixins/scssLoader.js'
  export default {
    name: 'MailSendDialog',
    props: {
      showMailSendDialogStatus: { type: Boolean, default: false },
      mailDialogStatus: { type: Object, default: { type: Boolean, default: false } },
      recipient: { type: String, default: '' }
    },
    mixins: [relationship, images, scssLoader],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      sendStrangerNotyDialog: () => import('~/components/mail/sendStrangerNotyDialog'),
      xinAttachmentDescriptionDialog: () =>
        import('~/components/mail/xinAttachmentDescriptionDialog'),
      mjsAttachmentDescriptionDialog: () =>
        import('~/components/mail/mjsAttachmentDescriptionDialog'),
      confirmToMjsDialog: () => import('~/components/mail/confirmToMjsDialog')
    },
    data() {
      const mailForm = {
        recipient: '',
        mailTitle: this.$t('player_mail'),
        mailContent: '',
        xinCoin: ''
      }
      const strangerText = `(${this.$t('stranger')})`
      const sameGuildText = `(${this.$t('sameGuild')})`
      return {
        valid: false,
        mailFee: 0,
        showMailSendDialogStatusTmp: this.showMailSendDialogStatus,
        mailForm,
        senderTmp: {},
        selectedFriend: '',
        showAttachmentDescriptionStatus: false,
        showMjsAttachmentDescriptionStatus: false,
        showConfirmToMjsDialogStatus: false,
        confirmSendStatus: false,
        confirmLeaveStatus: false,
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        recipientMode: 'friend',
        searchInput: '',
        playerInfo: {},
        strangerInfo: {
          username: '',
          level: 0,
          vipLevel: 0,
          thumbUrl: '',
          online: false,
          money: 0,
          guildName: ''
        },
        showNicknameWithFullname: false,
        sendStrangerNotyDialogStatus: false,
        strangerText,
        sameGuildText,
        recipientDisabledStatus: false,
        confirmSendBtnStatus: false,
        //避免正在帶入陌生人當中,又遭到resetStranger
        setSelectedFriendFromRecipientStatus: false,
        recipientStatus: true,
        generalFee: 0.02,
        mjsData: {
          name: '',
          xinCoin: 0,
          mjsCoin: 0
        },
        //寄件內容暫存
        temporaryContent: '',
        confirmDialogMailFee: 0
      }
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      friendList({ $store }) {
        // deep copy
        let friendList = cloneDeep($store.getters['social/friendList'])
        for (let i of friendList) {
          if (this.checkIsSameGuild(i.username)) {
            i.username = i.username + this.sameGuildText
          }
        }
        friendList.sort(function (a, b) {
          // 先比較 level
          if (a.level > b.level) {
            return -1
          } else if (a.level < b.level) {
            return 1
          } else {
            // 如果 level 相同，比較 username
            if (/^[a-zA-Z]/.test(a.username) && !/^[a-zA-Z]/.test(b.username)) {
              return -1
            } else if (!/^[a-zA-Z]/.test(a.username) && /^[a-zA-Z]/.test(b.username)) {
              return 1
            } else {
              // 如果都是英文或都是中文，依照字母順序或中文排序
              if (/^[a-zA-Z]/.test(a.username)) {
                return a.username.localeCompare(b.username, 'en')
              } else {
                return a.username.localeCompare(b.username, 'zh-Hant')
              }
            }
          }
        })
        return friendList
      },
      friendListOnlyUsername({ $store }) {
        return $store.getters['social/friendList'].map((item) => item.username)
      },
      blockList({ $store }) {
        return $store.getters['social/blockList']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      transactionFee({ $store }) {
        return $store.getters['role/transactionFee']
      },
      recipientList() {
        const header = {
          header:
            this.recipientMode == 'friend' ? this.$t('friend_list') : this.$t('strange_player')
        }
        const headerOtherGame = {
          header: this.$t('other_games_role')
        }
        const noFriendNoty = {
          noty: true,
          text: this.canSearchStranger
            ? this.$t('no_friend_with_recipient_noty')
            : this.$t('mail_send_text3')
        }
        let friendList = []
        let strangeList = []
        const hasRecipients = this.recipient?.length !== 0
        const hasFriend = this.friendList?.length !== 0
        //回信模式
        if (hasRecipients && this.recipientStatus) {
          //確認是不是陌生人
          const isFriend = this.isFriend(this.senderTmp.username)
          //如果是好友
          if (isFriend) {
            friendList = [header, ...this.friendList]
            strangeList = []
          }
          //如果是陌生人
          else {
            friendList = [header, ...(hasFriend ? this.friendList : [noFriendNoty])]
            strangeList = [
              header,
              {
                username: this.senderTmp.username + this.strangerText,
                facebookId: '',
                online: this.senderTmp.online,
                gender: this.senderTmp.gender,
                rank: this.senderTmp.rank,
                relation: 1,
                level: this.senderTmp.level,
                vipLevel: this.senderTmp.vipLevel,
                thumbUrl: this.senderTmp.thumbUrl,
                disabled: this.senderTmp.online && this.senderTmp.level == 0 ? true : false
              }
            ]
          }
        }
        //寄信模式
        else {
          friendList = [
            headerOtherGame,
            {
              otherGame: true,
              gameName: this.otherGamesRole.mjs.gameName,
              username: this.otherGamesRole.mjs.username,
              subTitle: this.otherGamesRole.mjs.subTitle
            },
            header,
            ...(hasFriend ? this.friendList : [noFriendNoty])
          ]
          strangeList =
            this.searchInput && this.searchInput.length != 0
              ? [
                  header,
                  {
                    username: this.strangerInfo.username + this.strangerText,
                    facebookId: '',
                    online: this.strangerInfo.online,
                    gender: this.strangerInfo.gender,
                    rank: this.strangerInfo.rank,
                    relation: 1,
                    level: this.strangerInfo.level,
                    vipLevel: this.strangerInfo.vipLevel,
                    thumbUrl: this.strangerInfo.thumbUrl,
                    disabled:
                      this.strangerInfo.online && this.strangerInfo.level == 0 ? true : false
                  }
                ]
              : []
        }
        friendList.forEach((item) => {
          item.disabled = (item.online && item.level == 0) || item.noty === true ? true : false
        })
        return this.recipientMode == 'friend' ? friendList : strangeList
      },
      feeMessage() {
        return this.selectedFriend !== this.userName && this.mailForm.xinCoin
          ? `${this.$t('mailFee')}：${this.mailFee}`
          : ''
      },
      isBindPhone({ $store }) {
        return $store.getters['role/isBind']
      },
      vipLevelUpThanGold({ $store }) {
        return $store.getters['role/vipLevel'] >= 3
      },
      level({ $store }) {
        return $store.getters['role/level']
      },
      count() {
        return this.mailForm.mailContent.length
      },
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      },
      isRecipientSelf() {
        return this.userName == this.searchInput
      },
      guildMemberList({ $store }) {
        return $store.getters['social/guildMemberList']
      },
      canSearchStranger({ $store }) {
        return $store.getters['mail/canSearchStranger']
      },
      otherGamesRole({ $store }) {
        return $store.getters['role/otherGamesRole']
      },
      isLegacyIOS() {
        // 是否為IOS 16以前的IOS裝置
        const isIOS =
          (this.$device.isIos || this.$device.isMacOS) &&
          this.$device.userAgent.match(/Version\/(\d+\.\d+)/)?.[1] < 16
        return isIOS
      },
      handleCurrentMaxLen() {
        const totalMaxLength = 120
        // IOS:16 以前的裝置，在輸入換行時，maxlength會計算為2字元
        if (this.isLegacyIOS) {
          const currentContent = this.mailForm.mailContent || ''
          const lineBreakCount = (currentContent.match(/\r\n|\r|\n/g) || []).length
          // 每個換行多彌補一個字元
          return totalMaxLength + lineBreakCount
        }
        return totalMaxLength
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showMailSendDialogStatus: {
        handler(status) {
          // 平台維護檢查已由 layout 處理

          this.showMailSendDialogStatusTmp = status
          //如果是回信的情況 帶入收件人
          if (status === true) this.setSelectedFriendFromRecipient()
          else {
            this.resetStatus()
            this.resetForm()
          }
        },
        immediate: true
      },
      selectedFriend: {
        async handler(val) {
          let mailTitle = this.$t('player_mail')
          if (val === this.userName) {
            this.temporaryContent = this.mailForm.mailContent
            mailTitle = this.$t('mj_mail')
            if (this.mailForm.xinCoin) {
              this.mailForm.mailContent =
                this.$t('mail_mj_content1', {
                  userName: this.userName
                }) +
                this.$t('mail_mj_content2', {
                  xinCoin: this.mailForm.xinCoin
                })
            } else {
              this.mailForm.mailContent = this.$t('mail_mj_content3')
            }
          } else {
            this.mailForm.mailContent = this.temporaryContent
            // 計算郵寄費
            if (this.mailForm.xinCoin) {
              this.mailFee = this.calculateTradingFee(this.mailForm.xinCoin)
            }
          }
          this.mailForm.mailTitle = mailTitle
          this.valid = await this.validateForm()
          //if val empty change to friend mode
          if (this.recipientMode == 'stranger') {
            if (!val) {
              this.recipientMode = 'friend'
              this.$refs.mailRecipient.isMenuActive = true
            } else {
              this.setPlayerInfo(val)
              this.sendStrangerNotyDialogStatus = true
            }
          }
        }
      },
      async 'mailForm.mailContent'() {
        this.valid = await this.validateForm()
      },
      async 'mailForm.xinCoin'() {
        // 防止輸入小數點及其他符號
        this.mailForm.xinCoin = this.mailForm.xinCoin.replace(/^(0+)|[^\d]+/g, '')
        this.$refs.xinCoinInput.lazyValue = this.mailForm.xinCoin
        this.valid = await this.validateForm()
        let mailTitle = this.$t('player_mail')
        //星幣改變時需要修正內容文字
        if (this.selectedFriend === this.userName) {
          mailTitle = this.$t('mj_mail')
          if (this.mailForm.xinCoin) {
            this.mailForm.mailContent =
              this.$t('mail_mj_content1', {
                userName: this.userName
              }) +
              this.$t('mail_mj_content2', {
                xinCoin: this.mailForm.xinCoin
              })
          } else {
            this.mailForm.mailContent = this.$t('mail_mj_content3')
          }
        }
        // 計算郵寄費
        if (this.mailForm.xinCoin) {
          this.mailFee = this.calculateTradingFee(this.mailForm.xinCoin)
        }
        this.mailForm.mailTitle = mailTitle
      },
      searchInput(val) {
        //卡特殊符號與長度12
        if (val) {
          this.searchInput = val
            .replace(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?\uFF01-\uFF5E\s]/g, '')
            .slice(0, 12)
          this.$refs.mailRecipient.lazySearch = this.searchInput
        }
        //只允許在點放大鏡後才是stranger mode
        this.recipientMode = 'friend'
        if (this.showNicknameWithFullname) {
          this.$validator.errors.remove('mail.recipient')
          this.showNicknameWithFullname = false
        }
        //如果在回信的情況下 當輸入值有變動就不在是回信模式
        if (this.setSelectedFriendFromRecipientStatus) {
          this.recipientStatus = false
        }
      },
      orientation: {
        handler(orientation) {
          //避免手機轉向後選單跑版
          if (orientation !== 0 && this.$refs.mailRecipient) {
            this.$refs.mailRecipient.isMenuActive = false
          }
        },
        immediate: true
      }
    },
    async mounted() {
      //在輸入框清空時重置stranger
      const self = this
      self.$watch(
        () => {
          return self.$refs.mailRecipient.internalSearch
        },
        (val) => {
          if (!val) {
            self.$nextTick(() => {
              self.resetStranger()
            })
          }
        }
      )
      //取得麻將之星的角色資訊
      const mjsRole = {}
      const responseMjsData = await self.$store.dispatch('role/getMjsRank')
      mjsRole.gameName = this.$t('mahjong_star')
      mjsRole.username = this.userName
      mjsRole.subTitle = responseMjsData.rank ? responseMjsData.rank : '-'
      this.$store.commit('role/SET_OTHER_GAMES_ROLE', { key: 'mjs', value: mjsRole })
    },
    methods: {
      calculateTradingFee(amount) {
        const isFriend = this.isFriend(this.selectedFriend)
        //檢查是否為陌生人
        let username = isFriend
          ? this.selectedFriend
          : this.selectedFriend.replace(this.strangerText, '')
        //檢查是否為同公會
        if (this.selectedFriend.includes(this.sameGuildText)) {
          username = this.selectedFriend.replace(this.sameGuildText, '')
        }
        let transactionFee = this.isInGuildMemberList(username)
          ? this.transactionFee
          : this.generalFee

        const amountStr = amount.toString(10)
        const fee = new Decimal(amountStr).mul(transactionFee)
        const feeStr = fee.toFixed(0, Decimal.ROUND_UP)
        return feeStr
      },
      formatPrice(value) {
        if (value !== undefined) {
          return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        } else {
          return 0
        }
      },
      closeDialog() {
        this.resetStatus()
        this.$emit('update:showMailSendDialogStatus', false)
        this.$store.commit('mail/SET_SHOW_NOTY_STATUS', false)
        //關閉寄件彈窗，不要讓信件列表滾動到最上面
        this.$store.commit('mail/SET_SCROLL_TOP_STATUS', false)
        this.$nuxt.$emit('root:mailDialogStatus', { show: true, name: '' })
      },
      resetStatus() {
        this.confirmSendStatus = false
        this.confirmLeaveStatus = false
      },
      resetForm() {
        this.mailForm = {
          recipient: '',
          mailTitle: this.$t('player_mail'),
          mailContent: '',
          xinCoin: ''
        }
        this.selectedFriend = ''
        this.$validator.reset()
      },
      openAttachmentDescriptionDialog() {
        if (this.selectedFriend === this.userName) {
          this.showMjsAttachmentDescriptionStatus = true
        } else this.showAttachmentDescriptionStatus = true
      },
      closeAttachmentDescriptionDialog() {
        this.showAttachmentDescriptionStatus = false
        this.$emit('update:showMailSendDialogStatus', true)
      },
      closeMjsAttachmentDescriptionDialog() {
        this.showMjsAttachmentDescriptionStatus = false
        this.$emit('update:showMailSendDialogStatus', true)
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      async prepareSendHandler() {
        // 平台維護檢查已由 layout 處理
        // 先更新公會成員列表
        await this.$store.dispatch('guild/guildMemberInfoList')
        const validate = await this.$validator.validate('mail.*')
        if (validate) {
          const xinCoin = this.mailForm.xinCoin
          const minimumBalance = 30000 // 最小轉出額度
          let title = ''
          let message = ''

          // 現有錢包扣除附件額度再扣除郵寄費
          const afterBalance = this.$store.getters['role/balance'] - xinCoin - this.mailFee
          if (minimumBalance > afterBalance) {
            // 轉出後，餘額必須至少30000
            //麻將之星
            if (this.selectedFriend === this.userName) {
              message =
                '<p class="mb-0 text-wrap">' +
                this.$t('mjs_mail_send_noty1', {
                  balance: `<span class="success--text font-weight-bold">30,000</span>`
                }) +
                '</p>'
            } else {
              message =
                '<p class="mb-0 text-wrap">' +
                this.$t('mail_send_noty1', {
                  balance: `<span class="success--text font-weight-bold">30,000</span>`
                }) +
                '</p>'
            }
          } else if (this.level == 0 || !this.vipLevelUpThanGold || this.isBindPhone === false) {
            // 【會員階級】黃金以上即可使用信件功能 必須綁定門號才能使用此功能
            message = this.$t(this.$UIConfig.mailIndex.sendNoty)
          }
          //通過檢查
          else {
            if (this.selectedFriend === this.userName) {
              //麻將之星
              this.showConfirmToMjsDialogStatus = true
            }
            //普通寄信
            else {
              this.confirmDialogMailFee = this.mailFee
              this.confirmSendStatus = true
            }
          }

          if (message !== '') {
            title = this.$t('reminder')
            this.showNotyDialog(title, message)
          }
        }
      },
      async sendMailHandler() {
        const validate = await this.$validator.validate('mail.*')
        if (validate) {
          // call api
          const { mailTitle, mailContent, xinCoin } = this.mailForm
          //判斷送出名子是否包含'陌生人'或是'同公會'，若有就拿掉
          const recipient =
            this.recipientMode === 'stranger' && this.selectedFriend.includes(this.strangerText)
              ? this.selectedFriend.replace(this.strangerText, '')
              : this.selectedFriend.includes(this.sameGuildText)
              ? this.selectedFriend.replace(this.sameGuildText, '')
              : this.selectedFriend
          // : this.selectedFriend
          const minimumBalance = 30000
          let title = ''
          // 現有錢包扣除附件額度再扣除郵寄費
          const afterBalance = this.$store.getters['role/balance'] - xinCoin - this.mailFee
          if (minimumBalance > afterBalance) {
            const message =
              '<p class="mb-0 text-wrap">' +
              this.$t('mail_send_noty1', {
                balance: '<span class="success--text font-weight-bold"> ' + 30000 + '</span>'
              }) +
              '</p>'
            title = this.$t('reminder')
            this.showNotyDialog(title, message)
          } else {
            this.confirmSendBtnStatus = true
            const reqData = this.$wsPacketFactory.sendMail(
              recipient,
              mailTitle,
              mailContent,
              xinCoin
            )
            this.$wsClient.send(reqData)
            const res = await this.$xinUtility.waitEvent(
              this.$wsClient.receivedListeners,
              (data) => {
                return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.SEND)
              }
            )
            if (res.isSuccess) {
              // 成功刷新客戶端額度資訊
              this.$store.commit('role/SET_BALANCE', afterBalance)

              this.$refs.form.inputs.forEach((input) => {
                if (!input.disabled) {
                  input.reset()
                }
              })
              this.$notify.success(this.$t('send_out') + this.$t('success'))
              this.$emit('update:showMailSendDialogStatus', false)
              this.$store.commit('mail/SET_SHOW_NOTY_STATUS', false)
              //回信的模式 不要讓信件列表滾動到最上面
              if (this.recipient?.length != 0)
                this.$store.commit('mail/SET_SCROLL_TOP_STATUS', false)
              this.$nuxt.$emit('root:mailDialogStatus', { show: true, name: '' })
            } else {
              this.$notify.error(res.message)
            }
            this.$validator.reset('mail.*')
            this.confirmSendBtnStatus = false
            this.confirmSendStatus = false
          }
        }
      },
      async validateForm() {
        const self = this
        return await self.$validator.validate('mail.*')
      },
      async setSelectedFriendFromRecipient() {
        const { recipient } = this
        if (recipient?.length != 0) {
          const isFriend = this.isFriend(recipient)
          const isSameGuild = this.checkIsSameGuild(recipient)
          this.recipientDisabledStatus = true
          this.setLoading(true)
          const role = await this.getPlayerData(recipient)
          this.recipientDisabledStatus = false
          this.setLoading(false)
          this.senderTmp = role
          if (isSameGuild) {
            this.selectedFriend = role.username + `(${this.$t('sameGuild')})`
          } else if (isFriend) {
            this.selectedFriend = role.username
          } else {
            const strangerName = role.username + `(${this.$t('stranger')})`
            this.strangerInfo.username = strangerName
            this.strangerInfo.thumbUrl = role.thumbUrl
            this.strangerInfo.level = role.level
            this.strangerInfo.vipLevel = role.vipLevel
            this.strangerInfo.money = role.money
            this.strangerInfo.online = role.online
            this.strangerInfo.guildName = role.guildName
            this.recipientMode = 'stranger'
            this.selectedFriend = strangerName
          }
          this.setSelectedFriendFromRecipientStatus = true
        } else {
          this.setSelectedFriendFromRecipientStatus = true
        }
      },
      //千分位
      formatNumber(num) {
        return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
      },
      async searchPlayer() {
        const internalSearch = this.$refs.mailRecipient.internalSearch
        if (internalSearch) {
          const isFriend = this.isFriend(internalSearch)
          if (!isFriend) {
            this.recipientMode = 'stranger'
            this.recipientDisabledStatus = true
            this.setLoading(true)
            const role = await this.getPlayerData(internalSearch)
            this.recipientDisabledStatus = false
            this.setLoading(false)
            this.strangerInfo.username = internalSearch
            this.strangerInfo.thumbUrl = role.thumbUrl
            this.strangerInfo.level = role.level
            this.strangerInfo.vipLevel = role.vipLevel
            this.strangerInfo.money = role.money
            this.strangerInfo.online = role.online
            this.strangerInfo.guildName = role.guildName
            this.$refs.mailRecipient.isMenuActive = true
          }
        } else if (this.selectedFriend?.length == 0) {
          this.$refs.mailRecipient.isMenuActive = false
          this.$validator.errors.remove('mail.recipient')
          this.$validator.errors.add({
            field: 'mail.recipient',
            msg: this.$t('search_nickname_with_fullname')
          })
          this.showNicknameWithFullname = true
        }
      },
      resetStranger() {
        if (
          (!this.selectedFriend || this.selectedFriend.length == 0) &&
          this.setSelectedFriendFromRecipientStatus
        ) {
          this.recipientMode = 'friend'
          this.searchInput = ''
        }
      },
      clearRecipientField() {
        this.selectedFriend = ''
        this.searchInput = ''
        this.$refs.mailRecipient.isMenuActive = true
      },
      closeMenu() {
        this.$refs.playerInfoMenu.isActive = false
      },
      async setPlayerInfo(name) {
        if (this.recipientMode == 'stranger') {
          const strangerInfo = this.strangerInfo
          const info = {
            username: strangerInfo.username,
            thumbUrl: strangerInfo.thumbUrl,
            level: strangerInfo.level,
            vipLevel: strangerInfo.vipLevel,
            online: strangerInfo.online,
            money: strangerInfo.money,
            guildName: strangerInfo.guildName
          }
          this.playerInfo = info
        } else {
          //刪掉同公會文字
          name = name.replace(this.sameGuildText, '')
          let role = await this.getPlayerData(name)
          this.playerInfo = role
        }
      },
      selectItem(item) {
        this.selectedFriend = item
        this.$refs.mailRecipient.isMenuActive = false
      },
      setLoading(status) {
        if (status) this.$nuxt.$loading.start()
        else this.$nuxt.$loading.finish()
      },
      isFriend(name) {
        return this.friendList.filter((item) => item.username == name).length > 0
      },
      isInGuildMemberList(name) {
        // 使用和 checkIsSameGuild 同樣的資料來源
        const guildMemberList = this.$store.getters['guild/guildMemberInfoList']

        if (!name) return false

        const result =
          guildMemberList.findIndex((item) => item.name.toLowerCase() === name.toLowerCase()) > -1
        return result
      },
      async getPlayerGuild(name) {
        const reqData = this.$wsPacketFactory.getUserGuild(name)
        this.$wsClient.send(reqData)
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return data.isFeature(this.$xinConfig.FEATURE.GUILD.TYPE.GET_USER_GUILD)
        })
        return res
      },
      errorImgHandler(item) {
        item.thumbUrl = this.defaultImg
      },

      stringNullOrEmpty(word) {
        return isNullOrEmpty(word)
      }
    },
    beforeDestroy() {
      this.$store.commit('mail/SET_OPEN_SEND_MAIL_DIRECTLY', false)
    }
  }
</script>
<style lang="scss" scoped>
  $card-fill: map-get($colors, 'card-fill');
  $primary: map-get($colors, 'primary');
  $default-content: map-get($colors, 'default-content');
  $error: map-get($colors, 'error');
  #recipient-chip::v-deep {
    button {
      color: map-get($colors, grey-3);
    }
  }
  .v-list {
    background-color: $card-fill !important;
  }

  .theme--dark::v-deep {
    .v-messages {
      &:not(.error--text) {
        color: $default-content !important;
      }

      /* 保持錯誤訊息為紅色 */
      &.error--text {
        color: $error !important;
      }
    }

    /* focus 狀態的提示訊息 */
    .v-input--is-focused {
      .v-messages:not(.error--text) {
        color: $primary !important;
      }
    }
  }
</style>
<!-- 此區不加scoped -->
<style lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  .sendMailMenu {
    .v-badge__badge::after {
      border-color: $dialog-fill !important;
    }
  }
  .custom-autocomplete {
    .v-input__control {
      .v-input__slot {
        .v-select__slot {
          .v-input__append-inner {
            margin-top: 25px !important;
          }
        }
      }
    }
  }
</style>
