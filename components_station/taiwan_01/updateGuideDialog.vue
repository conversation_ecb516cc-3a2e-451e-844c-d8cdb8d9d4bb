<template>
  <div class="update-guide-dialog">
    <!-- 彈窗遮罩 -->
    <!-- 因為 v-dialog 添加了 attach，預設遮罩會不同層，所以改用 v-overlay -->
    <v-overlay
      v-if="guideList.length"
      absolute
      color="#212121"
      opacity="0.9"
      :value="showUpdateGuideDialogStatusTmp"
      @click="setDialog(false)"
    ></v-overlay>
    <!-- 關閉按鈕 -->
    <v-btn
      v-if="guideList.length"
      icon
      :style="closeStyle"
      @click="setDialog(false)"
      ref="closeBtn"
    >
      <v-icon>mdi-close</v-icon>
    </v-btn>
    <!-- 彈窗 -->
    <!-- 因為之後出現的 v-dialog，會覆蓋現有的彈窗，所以添加 attach 抽離預設機制，避免被覆蓋 -->
    <!-- z-index 層數，則由 .update-guide-dialog 決定 -->
    <v-dialog
      max-width="327"
      hide-overlay
      attach
      v-model="showUpdateGuideDialogStatusTmp"
      ref="dialog"
    >
      <div :style="innerStyle">
        <swiper v-if="guideList.length" :options="swiperOption" ref="swiper">
          <swiper-slide v-for="item in guideList" class="swiper-slide" :key="item.id">
            <v-img
              :src="item.guideImg"
              class="h-100-percent"
              @load="imageLoaded(item.timer)"
              @error="setDefauiltImg(item.id)"
            >
              <template v-slot:placeholder>
                <v-row class="fill-height ma-0" align="center" justify="center">
                  <v-progress-circular indeterminate color="grey lighten-5"></v-progress-circular>
                </v-row>
              </template>
            </v-img>
          </swiper-slide>
        </swiper>
        <v-row v-else class="fill-height ma-0 progress" align="center" justify="center">
          <v-progress-circular indeterminate color="grey lighten-5"></v-progress-circular>
        </v-row>
      </div>
    </v-dialog>
    <!-- 輪播圖切換按鈕：前一張 -->
    <v-btn
      v-if="guideList.length > 1"
      fab
      small
      class="swiper-custom-button"
      :style="swiperPrevStyle"
      @click.prevent="slideButton('prev')"
    >
      <span class="material-symbols-outlined font-weight-bold"> navigate_before </span>
    </v-btn>
    <!-- 輪播圖切換按鈕：下一張 -->
    <v-btn
      v-if="guideList.length > 1"
      fab
      small
      class="swiper-custom-button"
      :style="swiperNextStyle"
      @click.prevent="slideButton('next')"
    >
      <span class="material-symbols-outlined font-weight-bold"> navigate_next </span>
    </v-btn>
    <!-- 輪播圖頁碼圓點 -->
    <div
      v-if="guideList.length > 1"
      class="swiper-pagination"
      slot="pagination"
      ref="swiperPagination"
      :style="paginationStyle"
    ></div>
  </div>
</template>
<script>
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
  import debounce from 'lodash/debounce'
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'

  const NUXT_ENV = process.env.NUXT_ENV
  const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

  export default {
    name: 'updateGuideDialog',
    mixins: [hiddenScrollHtml],
    components: {
      Swiper,
      SwiperSlide
    },
    props: {
      showUpdateGuideDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        isDataLoaded: false,
        debounceGetDialogSize: null,
        guideInfo: {},
        guideList: [],
        dialog: {
          width: 0,
          height: 517,
          padding: 8
        },
        closeBtn: {
          width: 0,
          height: 0
        },
        innerStyle: {},
        isImageLoaded: 0,
        imageLoadedCount: 0,
        defauiltImg: require('~/assets/image/update_guide_dialog_defauilt.png'),
        swiperOption: {
          allowTouchMove: true, // 啟用觸摸滑動
          slidesPerView: 1,
          spaceBetween: 16,
          fade: {
            crossFade: true
          },
          observer: true,
          observeParents: true,
          autoplay: {
            delay: 2000, // 設定自動切換的時間間隔為2秒
            disableOnInteraction: false // 用戶操作後不要禁用自動切換
          },
          pagination: {
            clickable: true // 允許點擊圓點來切換幻燈片
          }
        }
      }
    },
    computed: {
      showUpdateGuideDialogStatusTmp: {
        get() {
          return this.showUpdateGuideDialogStatus
        },
        set(val) {
          this.setDialog(val)
        }
      },
      closeStyle() {
        return {
          transform: `translate(calc(-100% + ${
            this.dialog.width / 2 + this.dialog.padding / 2
          }px), -${this.dialog.height / 2 + this.closeBtn.height}px)`
        }
      },
      swiperPrevStyle() {
        return {
          transform: `translate(${-this.dialog.width / 2 + this.dialog.padding * 2}px,-50%)`
        }
      },
      swiperNextStyle() {
        return {
          transform: `translate(calc(-100% + ${
            this.dialog.width / 2 - this.dialog.padding * 2
          }px),-50%)`
        }
      },
      paginationStyle() {
        return {
          transform: `translate(-50%, ${this.dialog.height / 2}px)`
        }
      }
    },
    watch: {
      isDataLoaded: {
        handler(loaded) {
          loaded && this.init()
        }
      },
      imageLoadedCount: {
        handler(count) {
          if (count === this.guideList.length) {
            const data = JSON.stringify({ startDate: this.guideInfo.startDate })
            localStorage.setItem('updateGuideDialog', data)
          }
        }
      }
    },
    async created() {
      await this.getGuideList()
      this.isDataLoaded = true
    },
    mounted() {
      // 把 getDialogSize 包到 debounce 中，並指定到 debounceGetDialogSize，供 removeEventListener 移除事件時使用
      this.debounceGetDialogSize = debounce(this.getDialogSize, 100)
      window.addEventListener('resize', this.debounceGetDialogSize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.debounceGetDialogSize)
    },
    methods: {
      setDialog(val) {
        this.$emit('update:showUpdateGuideDialogStatus', val)
      },
      // 初始化 swiper pagination
      async initSwiperPagination() {
        await this.$nextTick()
        const swiper = this.$refs['swiper'].$swiper
        if (swiper) {
          swiper.params.pagination.el = this.$refs['swiperPagination']
          swiper.pagination.init()
          swiper.pagination.render()
          swiper.pagination.update()
        }
      },
      // 取得關閉按鈕的寬高
      getCloseBtnSize() {
        const closeBtn = this.$refs['closeBtn'].$el
        if (closeBtn) {
          this.closeBtn.width = closeBtn.clientWidth
          this.closeBtn.height = closeBtn.clientHeight
        }
      },
      // 取得彈窗的寬高
      async getDialogSize() {
        await this.$nextTick()
        const dialog = this.$refs['dialog'].$refs.dialog
        if (dialog) {
          this.dialog.width = dialog.clientWidth
          this.dialog.height = dialog.clientHeight
        }
      },
      // 設定彈窗預設寬高（若圖片載入完成，則移除）
      setInnerStyle() {
        this.innerStyle = this.isImageLoaded
          ? { padding: '8px' }
          : {
              padding: '8px',
              height: `${this.dialog.height - this.dialog.padding * 2}px`,
              'max-height': `calc(85vh - ${this.dialog.padding * 2}px)`
            }
      },
      // 切換輪播圖
      slideButton(leftAndRight) {
        const swiper = this.$refs['swiper'].$swiper
        if (swiper) {
          if (leftAndRight === 'prev') swiper.slidePrev()
          if (leftAndRight === 'next') swiper.slideNext()
        }
      },
      imageLoaded(timer) {
        this.isImageLoaded += 1
        this.imageLoadedCount += 1
        timer && clearTimeout(timer)
        // 第一張圖片 loading 完成，以防圖片出現延遲，抓不到圖片實際尺寸
        // 等候 0.1 秒後，移除彈窗內容預設高度，並取得圖片寬高
        if (this.isImageLoaded === 1) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.setInnerStyle()
              this.getDialogSize()
            }, 100)
          })
        }
      },
      // 取得彈窗的輪播圖清單
      async getGuideList() {
        try {
          const guideData = await this.$axios.get(
            process.env.IMAGE_URL +
              `/update_guide/guide_provider/${loadConfig.client_id}/guide_provider.json?` +
              Math.random()
          )
          this.guideInfo = {
            startDate: guideData.data.startDate,
            endDate: guideData.data.endDate
          }
          this.guideList = guideData.data.list.map((item) => ({
            ...item,
            guideImg: `${process.env.IMAGE_URL}/update_guide/guide_img/${loadConfig.client_id}/${item.guideImg}`,
            timer: setTimeout(() => this.setDefauiltImg(item.id), 5000) // 如果卡 loading 超過 5 秒，就塞預設圖片
          }))
          this.checkGuideList()
        } catch (e) {
          // 沒取得輪播圖清單，無法得知是否需顯示彈窗(起始、結束日期皆不知)，故直接不顯示
          this.setDialog(false)
        }
      },
      init() {
        this.setInnerStyle()
        this.getCloseBtnSize()
        this.getDialogSize()
        this.initSwiperPagination()
      },
      // 設定預設圖片，以防圖片載入失敗
      setDefauiltImg(id) {
        this.imageLoadedCount -= 1
        const match = this.guideList.find((item) => item.id === id)
        if (match) match.guideImg = this.defauiltImg
      },
      // 藉由 localStorage 與 GCP 取得的資料，檢查是否需顯示彈窗
      checkGuideList() {
        const ls = JSON.parse(localStorage.getItem('updateGuideDialog'))

        // 起始日期：localStorage 與 GCP 相同，不顯示彈窗
        if (ls && ls.startDate === this.guideInfo.startDate) {
          this.setDialog(false)
          return
        }

        // 確認目前是否在提示期間
        const startDate = new Date(this.guideInfo.startDate)
        const endDate = new Date(this.guideInfo.endDate)
        const nowDate = new Date()
        if (nowDate < startDate || nowDate > endDate) {
          this.setDialog(false)
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  $warning-color: map-get($colors, warning);
  .update-guide-dialog {
    z-index: 999;
  }
  ::v-deep .v-dialog {
    background-color: rgba(255, 255, 255, 0.5);
    max-height: 85%;

    .progress {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
    }

    .swiper-slide {
      height: 100%;
      background: rgba(33, 33, 33, 0.7);
    }
  }
  .v-btn,
  .swiper-pagination {
    z-index: 203;
    position: fixed;
    top: 50%;
    left: 50%;
    ::v-deep .swiper-pagination-bullet {
      background: #fff;
      opacity: 1;

      &:not(:last-child) {
        margin-right: 8px;
      }

      &.swiper-pagination-bullet-active {
        background: $warning-color;
      }
    }

    &.swiper-custom-button {
      background-color: rgba(255, 255, 255, 0.3) !important;
      z-index: 205;
    }
  }
</style>
