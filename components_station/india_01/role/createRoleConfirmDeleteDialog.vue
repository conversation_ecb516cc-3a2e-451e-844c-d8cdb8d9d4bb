<template>
  <v-dialog
    v-model="createRoleConfirmDeleteDialogStatusTmp"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card class="dialog-fill pa-4 pa-sm-6">
      <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">{{
        $t('reminder').toUpperCase()
      }}</v-card-title>
      <v-card-text class="px-0 py-6">
        <span class="custom-text-noto text-body-2 default-content--text">
          {{ $t('char_create_interrupt_noty') }}
        </span>
      </v-card-text>

      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
            ><v-btn
              :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="closeDialog"
            >
              {{ $t('cancel').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :color="$UIConfig.defaultBtnColor"
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              depressed
              @click="endAndLogout"
            >
              {{ $t('end_and_logout') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'CreateRoleConfirmDeleteDialog',
    props: {
      createRoleConfirmDeleteDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        createRoleConfirmDeleteDialogStatusTmp: this.createRoleConfirmDeleteDialogStatus
      }
    },
    watch: {
      createRoleConfirmDeleteDialogStatus: function (val) {
        this.createRoleConfirmDeleteDialogStatusTmp = val
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:createRoleConfirmDeleteDialogStatus', false)
      },
      isNullOrEmpty(str) {
        return str === '' || str === null || str === undefined
      },
      endAndLogout() {
        if (this.isThirdParty) {
          this.$store.commit('role/SET_LOGIN_LAST_TIME', 0)
          this.$store.commit('role/SET_LOGIN_TIMMER_STATE', false)
          const reqData = this.$wsPacketFactory.logout()
          this.$wsClient.send(reqData)
          this.$wsClient.disconnect()
          this.$store.dispatch('clear')
          this.$cookies.remove('xinToken', { path: '/' })
        }
        this.closeDialog()
        this.$nuxt.$emit('root:showPhoneNumBindingDialogStatus', false)
        this.$nuxt.$emit('root:showCreateRoleDialogStatus', false)
        this.$nuxt.$emit('root:showLoginDialogStatus', {
          show: true,
          onCancelNotify:
            this.showGameModeStatus && this.$route.params.mode === 'play'
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
        })
      }
    },
    computed: {
      fbName({ $store }) {
        return $store.getters['role/fbName']
      },
      facebookId() {
        return this.$store.getters['role/facebookId']
      },
      isThirdParty({ $store }) {
        const type = $store.getters['role/loginType']
        return type === 6 || type === 5 || type === 4
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    }
  }
</script>

<style lang="scss" scoped></style>
