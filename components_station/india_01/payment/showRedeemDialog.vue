<template>
  <div>
    <v-dialog
      v-model="showRedeemDialogStatusTmp"
      max-width="400px"
      persistent
      content-class="rounded-lg"
    >
      <v-card tile color="dialog-fill" class="rounded-t-lg">
        <v-card-title
          class="custom-text-noto text-subtitle-1 justify-center gradient-primary-left button-content--text"
        >
          {{ $t('serial_redeem').toUpperCase() }}
        </v-card-title>
        <v-card-text class="pb-0 px-4 pt-4 px-sm-6 pt-sm-6">
          <v-text-field
            v-model="cardSerialNumber"
            data-vv-scope="redeemGroup"
            v-validate="{ required: true, redeem_code_limit: true }"
            @keydown.enter="sendRedeemCodeEvent(cardSerialNumber)"
            :error-messages="errors.first('redeemGroup.serial_number')"
            :placeholder="$t('please_enter_serial')"
            :label="$t('serial_number') + '*'"
            class="input-height"
            name="serial_number"
            maxlength="32"
            filled
            shaped
          />
        </v-card-text>
        <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
          <v-row no-gutters justify="end">
            <v-col class="pr-2" :cols="breakpoint.xsOnly ? '6' : 'auto'">
              <v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="closeDialog"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col class="pl-2" :cols="breakpoint.xsOnly ? '6' : 'auto'"
              ><v-btn
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                depressed
                :disabled="disabledSubmit"
                @click="sendRedeemCodeEvent(cardSerialNumber)"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="showRedeemSuccessStatus"
      persistent
      max-width="400px"
      content-class="rounded-lg"
    >
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center pa-0">
          {{ $t('hint').toUpperCase() }}
        </v-card-title>
        <v-card-text class="default-content--text px-0 py-6">
          <span class="redeem-font-style">{{ showMsg }}</span>
          <br />
          <span v-show="showDownloadRow">
            <i18n path="redeem_success_noty2">
              <template v-slot:mark>＊</template>
            </i18n>
          </span>
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-spacer />
          <v-btn
            :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
            :color="$UIConfig.defaultBtnColor"
            elevation="0"
            @click="doSure"
          >
            {{ $t('sure').toUpperCase() }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  import analytics from '@/mixins/analytics.js'
  const redeemMgr = require(`~/mixins_station/${STATION}/redeemMgr`).default
  export default {
    name: 'ShowRedeemDialog',
    mixins: [analytics, redeemMgr],
    props: {
      showRedeemDialogStatus: { type: Boolean, default: false }
    },
    data() {
      const errorKey = ['門號認證']
      const redeemKey = ['星幣', '銀幣']
      const starCoin = ['星幣']
      const officialMemberKey = ['成為正式會員']
      const systemMessageType = 0
      return {
        errorKey,
        redeemKey,
        starCoin,
        officialMemberKey,
        systemMessageType,
        disabledSubmit: true,
        showDownloadRow: false,
        showRedeemDialogStatusTmp: this.showRedeemDialogStatus,
        showRedeemSuccessStatus: false,
        cardSerialNumber: '',
        requestMsg: '',
        showMsg: ''
      }
    },
    watch: {
      showRedeemDialogStatus: {
        async handler(value) {
          this.showRedeemDialogStatusTmp = value
        }
      },
      cardSerialNumber: {
        async handler() {
          const validateFormName = 'redeemGroup.*'
          const validate = await this.$validator.validate(validateFormName)
          this.disabledSubmit = !validate
        },
        deep: true
      }
    },
    async created() {},
    async beforeDestroy() {},
    methods: {
      removeDash: (str) => (str.startsWith('-') ? str.slice(1) : str),
      showRedeemRequest(request) {
        // 實際執行處理邏輯
        if (Array.isArray(request)) {
          request.forEach((req) => {
            this.redeemHandler(req)
          })
        } else {
          this.redeemHandler(request)
        }
      },
      redeemHandler(req) {
        const type = req.type
        if (type === 0) {
          this.requestMsg = this.stringNullOrEmpty(req.message) ? '' : this.removeDash(req.message)
          if (/lang_(152|422)/.test(this.requestMsg)) {
            const isOfficialMember = this.vipLevel > 0
            const giftResponseMsg = isOfficialMember
              ? this.$t('redeem_gift_noty1')
              : this.$t('redeem_gift_noty')
            if (!isOfficialMember) {
              this.$store.commit('role/SET_LEVEL', 1)
              this.$store.commit('role/SET_VIPLEVEL', 1)
            }
            this.onRedeemSuccess(giftResponseMsg, false)
          } else {
            this.$notify.error(this.requestMsg)
          }
        } else if (type === 11) {
          const redeemNoty = this.$t('redeem_success_noty1', {
            item: this.$t('redeem_coupon')
          })
          const kindHandlers = {
            0: () => this.$notify.error(this.$t('top_up_fail2')),
            1: () => this.$notify.error(this.$t('top_up_fail3')),
            2: () => this.$notify.error(this.$t('redeem_fail')),
            3: () => this.$notify.error(this.$t('redeem_fail1')),
            4: () => this.$notify.error(this.$t('redeem_fail2')),
            5: () => {
              this.onRedeemSuccess(redeemNoty, true)
              this.resetFieldText()
            }
          }
          ;(kindHandlers[req.kind] || (() => this.$notify.error(this.$t('redeem_data_error'))))()
        } else if (type === 187) {
          const redeemNoty = this.$t('redeem_success_noty1', {
            item: this.$t('redeem_coupon')
          })
          this.onRedeemSuccess(redeemNoty, true)
        } else if (type === 4) {
          this.updateUserData()
        } else {
          this.$notify.error(this.$t('redeem_data_error'))
        }
      },
      async onRedeemSuccess(message, showDownloadRow) {
        this.showMsg = message
        this.showDownloadRow = showDownloadRow
        this.showRedeemDialogStatusTmp = false
        this.showRedeemSuccessStatus = true
      },
      updateUserData() {
        setTimeout(() => {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())
          //首儲更新玩家資料
          if (this.checkItemMatch(this.requestMsg, this.officialMemberKey))
            this.$store.commit('role/SET_VIPLEVEL', 1)
        }, 100)
      },

      resetFieldText() {
        this.$validator.reset('form.*')
        this.disabledSubmit = true
        this.cardSerialNumber = ''
      },
      closeDialog() {
        this.resetFieldText()
        this.$nuxt.$emit('root:showRedeemDialogStatus', false)
      },
      async sendRedeemCodeEvent(redeemCode) {
        const request = await this.redeemCodeRequest(redeemCode.toUpperCase())
        if (request) this.showRedeemRequest(request)
      },
      getShowMsg() {
        if (this.checkItemMatch(this.requestMsg, this.officialMemberKey))
          return this.$t('redeem_success_noty')

        return this.$t('redeem_success_noty1', { item: this.getSlicString(this.requestMsg, ' ') })
      },
      goDownload() {
        this.closeDialog()
        if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
          const url = 'https://www.xin-stars.com/goStore'
          this.$lineOpenWindow.open(url)
        } else {
          this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
        }
      },
      getSlicString(message, sliceKey) {
        const messageItemIdx = message.indexOf(sliceKey) + 1
        const returnMessage = this.formatNumberWithCommas(message.slice(messageItemIdx))
        return returnMessage
      },
      checkItemMatch(message, keys) {
        return keys.some((key) => message.includes(key))
      },
      formatNumberWithCommas(text) {
        const numbersOnly = text.replace(/[^\d]/g, '')
        const formattedNumber = numbersOnly.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        return text.replace(/\d+/g, formattedNumber)
      },
      doSure() {
        this.showDownloadRow = false
        this.showRedeemSuccessStatus = false
        this.$nuxt.$emit('root:showRedeemDialogStatus', false)
      }
    },
    computed: {
      level({ $store }) {
        return $store.getters['role/level']
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    }
  }
</script>
<style lang="scss" scoped>
  .redeem-font-style {
    white-space: pre-wrap;
  }
</style>
