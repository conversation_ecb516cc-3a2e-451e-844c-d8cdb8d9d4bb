<template>
  <v-dialog
    v-model="showPhoneNumberDialogTmp"
    :max-width="UISetting.dialogWidth"
    persistent
    transition="dialog-transition"
    :fullscreen="$vuetify.breakpoint.smAndDown"
  >
    <v-card class="rounded-lg rounded-b-0 transparent">
      <customDialogTitle
        :title="$t('phone_login_title').toUpperCase()"
        @closeDialog="closeDialog"
      />
      <v-card-text class="py-6">
        <v-row no-gutters>
          <v-col :cols="isMobileDevice || !UISetting.showQRCode ? '12' : '6'">
            <v-row no-gutters v-if="isMobileDevice && UISetting.showQRCode" class="mb-6">
              <i18n
                path="qpp_advertisement1"
                tag="span"
                class="grey-2--text text-body-2 custom-text-noto"
              >
                <template v-slot:QPPBAG>
                  <span
                    class="primary--text text-decoration-underline"
                    style="cursor: pointer"
                    @click="gotoQPP"
                    >{{ $t('qpp_bag') }}</span
                  >
                </template>
                <template v-slot:HERE>
                  <span
                    class="primary--text text-decoration-underline"
                    style="cursor: pointer"
                    @click="gotoQPP"
                    >{{ $t('here') }}</span
                  >
                </template>
              </i18n>
            </v-row>
            <v-row class="justify-center" no-gutters>
              <!-- 國家代碼 -->
              <v-col class="pb-0 pr-2" cols="5">
                <v-autocomplete
                  ref="login_country_code"
                  v-model="loginForm.countryCode"
                  v-validate="'required'"
                  :no-data-text="$t('no_search_data')"
                  :disabled="disableInputStatus"
                  :error-messages="errors.first('login.country_code')"
                  :items="$phoneNumberValitator.codeList"
                  :item-text="loginForm.countryCode"
                  :label="$t('country_code')"
                  data-vv-name="country_code"
                  data-vv-scope="login"
                  filled
                  shaped
                  prefix="+"
                  class="mb-0"
                >
                  <template v-slot:item="data">+{{ data.item }} </template>
                </v-autocomplete>
              </v-col>
              <!-- 電話號碼輸入 -->
              <v-col class="pb-0 pl-2" cols="7">
                <v-text-field
                  ref="phone_number"
                  v-model="loginForm.phoneNumber"
                  v-validate="'required|phone_number:login_country_code'"
                  :disabled="disableInputStatus"
                  type="number"
                  :error-messages="errors.first('login.phone_number')"
                  :label="$t('phone_number') + '*'"
                  data-vv-name="phone_number"
                  data-vv-scope="login"
                  autofocus
                  shaped
                  filled
                  class="mb-0"
                  hide-spin-buttons
                  @keydown.enter="sendVerifyCodeMessage"
                />
              </v-col>
              <v-col class="pt-0" cols="12">
                <v-btn
                  :disabled="sendVerifyCodeBtnStatus"
                  :color="$UIConfig.defaultBtnColor"
                  block
                  depressed
                  class="mb-7 button-content--text"
                  @click="sendVerifyCodeMessage"
                >
                  {{ $t('send_verify_code') }}
                </v-btn>
                <v-text-field
                  ref="verifyCode"
                  v-model="loginForm.verifyCode"
                  v-validate="'required|validate_length:5'"
                  :disabled="showVerifyCodeInputStatus"
                  type="number"
                  :error-messages="errors.first('login.verify_code')"
                  :label="$t('verify_code') + '*'"
                  data-vv-name="verify_code"
                  data-vv-scope="login"
                  autocomplete="off"
                  filled
                  shaped
                  hide-spin-buttons
                />
                <p v-if="companyPhoneNumber !== ''" class="text-sm-body-2 default-content--text">
                  {{ $t('device_white_noty1', { phoneNumber: companyPhoneNumber }) }}
                </p>
                <p v-if="serviceMail !== ''" class="text-sm-body-2 default-content--text">
                  {{ $t('device_white_noty3', { mail: serviceMail }) }}
                </p>
                <v-btn
                  :loading="loadingLoginBtnStatus"
                  :disabled="disableLoginBtnStatus"
                  class="button-content--text"
                  :color="$UIConfig.defaultBtnColor"
                  block
                  depressed
                  @click="loginHandler"
                >
                  {{ $t('login').toUpperCase() }}
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
          <!-- 使用v-show原因 手機上不呈現QR CODE但還是需要進行QPP基本的判斷 -->
          <v-col v-show="!isMobileDevice && UISetting.showQRCode" cols="6">
            <v-row no-gutters justify="center" align="center" class="fill-height">
              <v-divider vertical class="mx-auto" />
              <qpp-login
                @closePhoneNumEvent="closeDialog"
                :qpp-u-r-l="qppURL"
                :device-u-u-i-d="deviceUUID"
              />
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import qppUse from '@/mixins/qppUse.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  const STATION = process.env.STATION
  const apiDomain = require(`~/station/${STATION}/apiDomain.js`).default

  export default {
    name: 'PhoneNumberDialog',
    mixins: [analytics, qppUse, preLoginAction],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      qppLogin: () => import('~/components/login/qppBlock')
    },
    props: {
      showPhoneNumberDialog: { type: Boolean, default: false }
    },
    data() {
      const loginForm = {
        countryCode: '+886',
        phoneNumber: '',
        verifyCode: ''
      }

      return {
        loginForm,
        showPhoneNumberDialogTmp: this.showPhoneNumberDialog,
        sendVerifyCodeBtnStatus: false,
        disableLoginBtnStatus: false,
        disableInputStatus: false,
        showVerifyCodeInputStatus: true,
        loadingLoginBtnStatus: false
      }
    },
    computed: {
      UISetting() {
        return this.$UIConfig.phoneNumber
      },
      countryCode() {
        const ct = require('countries-and-timezones')
        const timezone = ct.getTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)
        const countryCode = this.$phoneNumberValitator.contryMap[timezone.countries[0]] || ''
        return countryCode
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      companyPhoneNumber({ $store }) {
        return $store.getters[`${STATION}/companyInfo/phoneNumber`]
      },
      serviceMail({ $store }) {
        return $store.getters[`${STATION}/companyInfo/serviceMail`]
      },
      isMobile({ $device }) {
        // ipad 參照：https://stackoverflow.com/questions/57776001/how-to-detect-ipad-pro-as-ipad-using-javascript
        let isIpad = false
        if (process.client) {
          isIpad =
            (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 0) ||
            navigator.platform === 'iPad'
        }

        return $device.isMobile || isIpad
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      isVi({ $store }) {
        return $store.$i18n.locale === 'vi-vn'
      },
      isMobileDevice() {
        return (
          this.$device.isMobileOrTablet ||
          (this.$device.isDesktopOrTablet && this.$device.isMacOS) ||
          (this.$device.isDesktopOrTablet && this.$device.isSamsung)
        )
      }
    },
    watch: {
      showPhoneNumberDialog: {
        async handler(status) {
          this.showPhoneNumberDialogTmp = status
          this.loginForm = {
            countryCode: this.countryCode,
            phoneNumber: '',
            verifyCode: ''
          }
          this.$validator.reset('login.*')
        },
        immediate: true
      },
      loginForm: {
        handler(val) {
          if (val.verifyCode.length === 5) {
            this.loginHandler()
          }
        },
        deep: true
      },
      maintainSystem: {
        handler(val) {
          if (val[0].maintaining) {
            this.closeDialog()
            this.$nuxt.$emit('root:showLoginDialogStatus', {
              show: false,
              onCancelNotify: () => {}
            })
          }
        },
        deep: true
      },
      qppURL: {
        async handler(val) {
          if (val !== '' && this.showPhoneNumberDialog === true) {
            if (apiDomain().qpp.autoGoToStatus && this.qppURLAvailable) {
              // 如果满足条件，执行 URL 跳转
              this.gotoQPP()
            }
          }
        },
        immediate: true
      }
    },
    methods: {
      closeDialog() {
        this.loginForm = {
          countryCode: this.countryCode,
          phoneNumber: '',
          verifyCode: ''
        }
        this.$validator.reset('login.verifyCode')
        this.disableInputStatus = false
        this.sendVerifyCodeBtnStatus = false
        this.disableLoginBtnStatus = false
        this.showVerifyCodeInputStatus = true
        this.$emit('update:showPhoneNumberDialog', false)
      },
      async sendVerifyCodeMessage() {
        // 取得維護

        const validate = await Promise.all([
          this.$validator.validate('login.country_code'),
          this.$validator.validate('login.phone_number')
        ])

        if (validate.every((val) => val)) {
          this.$store.commit('xinProtocol/SET_LOGIN_STEP', 1)
          let phoneNumber = ''
          let countryCallingCode = ''
          phoneNumber = Number(this.loginForm.phoneNumber)
          countryCallingCode = this.loginForm.countryCode
          const fullPhoneNumber = '+' + countryCallingCode + phoneNumber
          this.$wsClient.send(this.$wsPacketFactory.getOTP(fullPhoneNumber))
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.OTP.ID)
          })
          // // 判斷有無發送過簡訊
          if (res.message !== '') {
            this.showVerifyCodeInputStatus = false
            this.$notify.info(res.message)
            this.sendVerifyCodeBtnStatus = true
          }

          this.$store.commit('role/SET_PHONENUMBER', this.loginForm.phoneNumber)
          this.disableInputStatus = true
          this.$refs.verifyCode.focus()
        }
      },
      async loginHandler() {
        // 取得維護

        const validate = await this.$validator.validate('login.*')
        if (validate) {
          this.$store.commit('xinProtocol/SET_LOGIN_STEP', 2)
          this.disableLoginBtnStatus = true
          this.loadingLoginBtnStatus = true
          const verifyCode = this.loginForm.verifyCode
          const xAgent = this.$cookies.get('xAgent')
          const reqData = this.$wsPacketFactory.verifyLogin({
            otp: verifyCode,
            device: xAgent,
            clientVersion: this.$xinServerConfig.clientVersion,
            serverVersion: this.$xinServerConfig.serverVersion,
            promote: this.$store.getters['social/promote']
          })
          this.$wsClient.send(reqData)
          try {
            const res = await this.$xinUtility.waitEvent(
              this.$wsClient.receivedListeners,
              (data) => {
                return data.isFeature(this.$xinConfig.FEATURE.OTP.ID)
              }
            )
            if (res.commandId === 136 || res.commandId === 135) {
              // 無此驗證或驗證碼已超過時效，請重新操作
              setTimeout(() => {
                this.disableLoginBtnStatus = false
                this.loginForm.verifyCode = ''
                this.loadingLoginBtnStatus = false
                this.sendVerifyCodeBtnStatus = false
                this.$validator.reset('login.verifyCode')
                this.disableInputStatus = false
              }, 1500)
              this.$notify.error(res.message)
            } else if (res.commandId === 133) {
              this.$store.commit('role/SET_ROLECOUNT', res.chars.length)
              this.$store.commit('role/SET_LIST', res.chars)
              this.$store.commit('role/SET_HASPWD', res.hasPwd === 255)

              setTimeout(async () => {
                this.closeDialog()
                this.$nuxt.$emit('root:showLoginDialogStatus', {
                  show: false,
                  onCancelNotify: () => {}
                })
                this.loginForm.verifyCode = ''
                this.loadingLoginBtnStatus = false
                if (res.chars.length > 0) {
                  await this.roleLogin(res.chars[0].username)
                  this.loginAnalytics('Webnumber')
                  this.$store.commit('role/SET_LOGIN_TYPE', 2)
                  // 成功登入，執行使用者登入前動作
                  this.callPreLoginAction()
                } else {
                  this.loginAnalytics('Webnumber')
                  this.$store.commit('role/SET_LOGIN_TYPE', 2)
                  this.$nuxt.$emit('root:showCreateRoleDialogStatus', true)
                }
              }, 1500)
            } else {
              this.$notify.error(this.$t('system_busy'))
            }
            this.$validator.reset('login.verifyCode')
          } catch (error) {
            console.log('phoneNumber Page', error)
          }
        }
      },
      async roleLogin(userName) {
        const title = this.$t('reminder')
        const promote = this.$store.getters['social/promote']

        this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
          serviceId: this.$xinConfig.LOGIN_SERVICE.ID,
          enable: false,
          connected: false
        })

        this.$wsClient.send(this.$wsPacketFactory.selectCharacter({ username: userName, promote }))
        let message = ''
        // 避免剛登入時連續跳出 noty info 的設計，先透過該值關閉通知
        this.$store.commit('mail/SET_FIRST_RECIVE', true)
        try {
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.LOGIN.ID)
          })
          // 因流程可能會未登入成功(角色在線、裝置驗證、裝置驗證失敗)
          if (
            res.type === 0 &&
            res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.PLAYER_INFO.ID
          ) {
            //成功登入時將倒數計時暫停
            this.$store.commit('role/SET_LOGIN_TIMMER_STATE', false)
            // 先發送前三個服務的加入請求
            const initialServices = [
              this.$xinConfig.LOTTERY_SERVICE.ID, // 進入摸彩服務
              this.$xinConfig.GAME_SERVICE.ID, // 進入遊戲服務
              this.$xinConfig.SOCIAL_SERVICE.ID // 進入社群服務
            ]

            initialServices.forEach((serviceId) => {
              this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId))
              this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                serviceId,
                enable: true,
                connected: true
              })
            })

            // 等待 SOCIAL_SERVICE 確認
            await this.$xinUtility
              .waitEvent(
                this.$wsClient.receivedListeners,
                (data) =>
                  data.protocolId === this.$xinConfig.PROTOCOL_ID.SERVICE &&
                  data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
              )
              .then(() => {
                this.$wsClient.send(this.$wsPacketFactory.initMail())
                // 延遲五秒後開啟新信件通知
                setTimeout(() => {
                  this.$store.commit('mail/SET_FIRST_RECIVE', false)
                }, 5000)
              })
              .catch((err) => {
                console.log('SOCIAL_SERVICE ERROR:', err)
              })
            // 初始化角色資料
            await this.$store.dispatch('role/profileInit', res)
            // 再加入公會服務
            this.$wsClient.send(
              this.$wsPacketFactory.getServiceJoin(this.$xinConfig.GUILD_SERVICE.ID)
            )
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: this.$xinConfig.GUILD_SERVICE.ID,
              enable: true,
              connected: true
            })

            //取得與伺服器時間差
            await this.$store.dispatch('xinProtocol/getServerLocalTimeDiff')

            setTimeout(async () => {
              this.$nuxt.$emit('root:showRoleDialogStatus', false)
              this.$nuxt.$emit('root:showLoginDialogStatus', {
                show: false,
                onCancelNotify: () => {}
              })
              // 站台差異註解
              if (this.$UIConfig.lock.getSlotIdentity) {
                const getSlotIdentity = await this.$waninNorthApi.user.getSlotIdentity({
                  PhoneNumber: this.phoneNumber
                })
                if (getSlotIdentity.status !== 200) {
                  this.$nuxt.$emit('root:showIdentityVerifiDialogStatus', true)
                }
              }
            }, 500)
            this.$nuxt.$emit('root:showWelcomeStatus', true)
            if (
              Object.prototype.hasOwnProperty.call(res, 'lastLogoutTime') &&
              res.lastLogoutTime === '1900/01/01 00:00:00'
            ) {
              this.createRoleAnalytics()
            }
          } else if (res.commandId === 136) {
            this.$notify.info(res.message)
            this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', true)
          } else if (res.commandId === 135) {
            message = res.message
            this.showNotyDialog(title, message)
          }
        } catch (error) {
          console.log('role Page', error)
        }
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      }
    }
  }
</script>
