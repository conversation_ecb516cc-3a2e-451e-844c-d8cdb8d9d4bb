<template>
  <div>
    <v-row no-gutters id="achievement-data-table">
      <v-col cols="12">
        <v-data-table
          class="grey-6 default-content--text text-regular--text bg-table"
          fixed-header
          @pagination="scollToTop"
          @update:page="handlePageChange"
          :hide-default-footer="$vuetify.breakpoint.xsOnly"
          :height="isCard ? height : '100%'"
          :items-per-page="itemsPage"
          :headers="achievementHeaders"
          :items="achievementList"
          :page="currentPage"
          :no-data-text="$t('no_data')"
          :footer-props="{
            'items-per-page-text': $t('items_per_page'),
            'items-per-page-all-text': $t('all'),
            'page-text': `{0}-{1} ${$t('total_page')} {2} ${$t('quantity')}`,
            'items-per-page-options': itemsPerPageOptions
          }"
        >
          <template v-if="achievementList.length === 0" v-slot:body>
            <tbody v-show="$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper d-flex justify-center">
                <td class="d-flex align-center" colspan="4">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
            <tbody v-show="!$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper">
                <td colspan="4">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
          </template>
          <template v-if="$vuetify.breakpoint.xsOnly" v-slot:footer="{ props: { pagination } }">
            <div class="v-data-footer">
              <v-row no-gutters>
                <v-col>
                  <div class="v-data-footer__select d-flex justify-start ml-3">
                    <span> {{ $t('items_per_page') }}</span>
                    <v-select
                      class="py-0 mt-3 mb-3"
                      v-model="select"
                      hide-details
                      height="32"
                      @input="onSelect"
                      :items="pagePaginationitem(itemsPerPageOptions)"
                    ></v-select>
                    <span class="v-data-footer__pagination">
                      {{ pagePagination(pagination) }}
                    </span>
                  </div>
                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col>
                  <v-btn
                    class="v-data-footer__icons-before"
                    icon
                    :disabled="pagination.pageStart === 0"
                    @click="currentPage = pagination.page - 1 === 0 ? 1 : pagination.page - 1"
                  >
                    <v-icon dark> mdi-chevron-left </v-icon>
                  </v-btn>
                  <v-btn
                    class="v-data-footer__icons-after"
                    icon
                    :disabled="pagination.pageStop === pagination.itemsLength"
                    @click="
                      currentPage =
                        pagination.page + 1 === pagination.pageCount
                          ? pagination.pageCount
                          : pagination.page + 1
                    "
                  >
                    <v-icon dark> mdi-chevron-right </v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </div>
</template>

<script>
  import cloneDeep from 'lodash/cloneDeep'
  import scssLoader from '@/mixins/scssLoader.js'
  export default {
    name: 'achievement',
    props: {
      userName: { type: String, required: true, default: '' },
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      height: {
        type: [Number, String],
        default: 550
      },
      itemsPerPage: {
        type: Number,
        default: 20
      },
      itemsPerPageOptions: {
        type: Array,
        default: () => [10, 20, 30, -1]
      }
    },
    mixins: [scssLoader],
    data() {
      return {
        achievementHeaders: [
          {
            text: this.$t('game_hall_name'),
            value: 'gameName',
            class: 'grey-4 primary--text  text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('type'),
            value: 'typeName',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('max_multiple'),
            value: 'multiple',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('highest_score'),
            value: 'score',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          }
        ],
        achievementList: [],
        select: this.itemsPerPage,
        itemsPage: this.itemsPerPage,
        currentPage: 1,
        //轉為Map的gameLobby資料
        gameLobbyMap: new Map(),
        gameFishMap: new Map()
      }
    },
    computed: {
      //原始gameLobby資料
      gameLobbyJson({ $store }) {
        return $store.getters['allStar/lobbys']
      },
      //原始gameFish資料
      gameFishJson({ $store }) {
        return $store.getters['allStar/fishs']
      }
    },
    async created() {
      //將game_lobby資料轉成map
      this.gameLobbyMap = this.convertGameLobbyMap(this.gameLobbyJson)
      //將game_fish資料轉成map
      this.gameFishMap = this.convertGameFishMap(this.gameFishJson)
      const achievementData = await this.getAchievement()
      //matchData，並依照遊戲編號為主，類別編號為輔 大到小排序
      this.achievementList = this.matchData(achievementData).sort((a, b) => {
        if (a.gameNo !== b.gameNo) {
          return b.gameNo - a.gameNo // Sort by gameNo in descending order
        } else {
          return b.type - a.type // Sort by type in descending order if gameNo is the same
        }
      })
    },
    methods: {
      async getAchievement() {
        // this.userName 角色名稱
        const res = await this.$store.dispatch('role/getAchievement', this.userName)
        if (res) {
          return res.achievements
        }
      },
      //match game_lobby & this.achievementList & game_fish
      matchData(datas) {
        let res = []
        datas.forEach((item) => {
          //match game_lobby
          if (this.gameLobbyMap.get(this.convertGameNo(item.gameNo))) {
            item.gameName = this.gameLobbyMap.get(this.convertGameNo(item.gameNo))
          }
          //match game_fish
          if (this.gameFishMap.get(item.type.toString())) {
            item.typeName = this.gameFishMap.get(item.type.toString())
          } else if (parseInt(item.gameNo) === 202) {
            item.typeName = this.$t('deathblow')
          } else {
            item.typeName = this.$t('round')
          }
          res.push(item)
        })
        return res
      },
      //將game_lobby資料轉成Map
      convertGameLobbyMap(datas) {
        return new Map(datas.map((item) => [item.code.slice(10, 14), item.text]))
      },
      //將game_Fish資料轉成Map
      convertGameFishMap(datas) {
        return new Map(datas.map((item) => [((item.fishType - 1) / 100).toString(), item.fishName]))
      },
      //轉換gameNo
      convertGameNo(gameNo) {
        return (parseInt(gameNo) + 2000).toString()
      },
      goDownload() {
        if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
          const url = 'https://www.xin-stars.com/goStore'
          this.$lineOpenWindow.open(url)
        } else {
          this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
        }
      },
      scollToTop() {
        //在 Safari 瀏覽器中，window.scrollTo()方法的behavior參數不支援'smooth'值，只支援'auto'和'instant'兩個值。
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      pagePaginationitem(itemArray) {
        let newItemArray = cloneDeep(itemArray)
        newItemArray[newItemArray.findIndex((x) => x === -1)] = this.$t('all')
        return newItemArray
      },
      onSelect() {
        if (this.select === this.$t('all')) this.itemsPage = -1
        else this.itemsPage = this.select
      },
      handlePageChange(page) {
        this.currentPage = page
      },
      pagePagination(pagination) {
        return pagination.pageCount === 0
          ? '-'
          : `${pagination.pageStart + 1}-${pagination.pageStop} ${this.$t('total_page')}
          ${pagination.itemsLength} ${this.$t('quantity')}`
      }
    }
  }
</script>

<style lang="scss">
  $grey-4: map-get($colors, 'grey-4');
  $grey-6: map-get($colors, 'grey-6');
  $default-content-color: map-get($colors, default-content);
  $bg-table-hover: map-get($colors, 'bg-table-hover');
  $text-regular: map-get($colors, 'text-regular');
  $btn-soft: map-get($colors, 'btn-soft');
  #achievement-data-table {
    .v-data-table-header-mobile {
      th {
        background: $grey-4 !important;
      }
    }
    .v-data-table {
      tbody {
        tr {
          &:hover {
            background: $grey-4 !important;
            //replaceColor
            background: $bg-table-hover !important;
          }
        }
      }
    }
    .v-data-footer {
      color: $default-content-color;
      //replaceColor
      color: $text-regular;
      background-color: $grey-6;
      //左右鍵
      .v-btn__content {
        .v-icon {
          color: $btn-soft;
        }
      }
      //下底線
      .v-input__slot::before {
        border-color: $text-regular !important;
      }
    }
    .v-select__selections {
      color: $default-content-color;
      //replaceColor
      color: $text-regular;
    }
    .v-icon {
      color: $default-content-color;
      //replaceColor
      color: $text-regular;
    }
  }
</style>
