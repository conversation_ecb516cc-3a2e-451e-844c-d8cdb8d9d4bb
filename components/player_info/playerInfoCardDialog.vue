<template>
  <div>
    <v-dialog
      v-model="showPlayerInfoCardTmp"
      :fullscreen="breakpoint.xsOnly"
      width="630"
      persistent
      scrollable
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-card class="pa-0" elevation="0" color="dialog-fill">
        fromLeaderboard:{{ fromLeaderboard }}
        <customDialogTitle :title="$t('player_info').toUpperCase()" @closeDialog="closeDialog" />
        <!-- 標籤 -->
        <v-tabs
          background-color="transparent"
          color="primary"
          class="px-4 px-sm-6"
          v-model="selectType"
          @change="preventScroll"
        >
          <v-tab class="a-link" href="#personal_status" key="personal_status">
            {{ $t('personal_status') }}
          </v-tab>
          <v-tab
            v-if="$UIConfig.playerInfoCardSetting.dailyListEnable"
            class="a-link"
            href="#daily_list"
            key="daily_list"
          >
            {{ $t('daily_list') }}
          </v-tab>
          <v-tab
            v-if="$UIConfig.playerInfoCardSetting.achievementEnable"
            class="a-link"
            href="#achievement"
            key="achievement"
          >
            {{ $t('achievement') }}
          </v-tab>
        </v-tabs>
        <!-- 內容 -->
        <v-card-text
          id="player-info-card"
          :class="[
            'h-100-percent px-4 pb-4 px-sm-6 pb-sm-6',
            breakpoint.xsOnly ? '' : ' scrollable-sm'
          ]"
        >
          <v-tabs-items v-model="selectType">
            <!-- 個人狀態 -->
            <v-tab-item key="personal_status" value="personal_status">
              <div :class="{ h643: breakpoint.xsOnly }">
                <characterInfo
                  :info.sync="selectPlayer"
                  is-card
                  :is-friend="isFriend"
                  :is-block="isBlock"
                />
                <v-divider class="mt-3 mb-3"></v-divider>
                <playerDetail :user-detail="selectPlayer" is-card />
                <v-row
                  no-gutters
                  class="justify-start mt-2"
                  v-if="selectPlayer.userName !== userName"
                >
                  <!-- 加為好友 breakpoint.xsOnly -->
                  <v-col cols="6" sm="4" md="3" class="pa-2 pl-0" v-if="!isFriend">
                    <v-btn
                      outlined
                      color="primary"
                      class="w-100"
                      @click="checkAddFriendInDialog(selectPlayer.username)"
                    >
                      <span class="material-symbols-rounded"> person_add </span>
                      <span class="pl-2 primary--text">
                        {{ $t('add_as_friend') }}
                      </span>
                    </v-btn>
                  </v-col>
                  <!-- 刪除好友 -->
                  <v-col cols="6" sm="4" md="3" class="pa-2 pl-0" v-else>
                    <v-btn
                      outlined
                      color="primary"
                      class="w-100"
                      @click="showConfirmDeleteFriendDialog(selectPlayer.userName)"
                    >
                      <v-icon> mdi-account-remove </v-icon>
                      <span class="pl-2 primary--text">
                        {{ $t('delete_friend') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 加黑名單 -->
                  <v-col cols="6" sm="4" md="3" class="pa-2 pr-0 pr-sm-2" v-if="!isBlock">
                    <v-btn
                      outlined
                      color="primary"
                      class="w-100"
                      @click="checkAddBlockInDialog(selectPlayer.username)"
                    >
                      <v-icon> mdi-account-cancel </v-icon>
                      <span class="pl-2">
                        {{ $t('add_to_blacklist') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!--  刪除黑名單 -->
                  <v-col cols="6" sm="4" md="3" class="pa-2 pr-0 pr-sm-2" v-else>
                    <v-btn
                      outlined
                      color="primary"
                      class="w-100"
                      @click="showConfirmDeleteBlockDialog(selectPlayer.userName)"
                    >
                      <v-icon> mdi-account-check </v-icon>
                      <span class="pl-2">
                        {{ $t('delete_block') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 密語 -->
                  <v-col cols="6" sm="4" md="3" class="pa-2 pl-0 pl-sm-2 pr-sm-0 pr-md-2">
                    <v-btn
                      outlined
                      color="primary"
                      :disabled="checkIsBlock(selectPlayer.userName)"
                      class="w-100"
                      @click="createMessageInDialog(selectPlayer)"
                    >
                      <span class="material-symbols-outlined"> sms </span>
                      <span class="pl-2">
                        {{ $t('whisper') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 寄信 -->
                  <v-col
                    cols="6"
                    sm="4"
                    md="3"
                    class="pa-2 pr-0 pl-sm-0 pb-sm-0 pr-sm-2 pl-md-2 pr-md-0 pb-md-2"
                  >
                    <v-btn
                      outlined
                      color="primary"
                      :disabled="checkIsBlock(selectPlayer.userName) || isOpenGameFrame"
                      class="w-100"
                      @click="openSendMailDialogInDialog(selectPlayer)"
                    >
                      <span class="material-symbols-outlined"> mail </span>
                      <span class="pl-2">
                        {{ $t('send_mail') }}
                      </span>
                    </v-btn></v-col
                  >
                  <!-- 檢舉 -->
                  <v-col cols="6" sm="4" md="3" class="pa-2 pl-0 pb-0 pl-sm-2 pl-md-0">
                    <v-btn
                      outlined
                      color="primary"
                      class="w-100"
                      @click="showReportDialog(selectPlayer.userName)"
                    >
                      <v-icon> mdi-account-alert </v-icon>
                      <span class="pl-2">
                        {{ $t('report') }}
                      </span>
                    </v-btn></v-col
                  >
                  <div
                    v-if="$UIConfig.playerInfoCardSetting.useVisitBtn"
                    class="mb-3"
                    :class="{
                      'w-150-px': breakpoint.xsOnly,
                      'w-173-px': !breakpoint.xsOnly
                    }"
                  ></div>
                </v-row>
              </div>
            </v-tab-item>
            <!-- 日榜 -->
            <v-tab-item key="daily_list" value="daily_list">
              <div :class="{ h643: breakpoint.xsOnly }">
                <div class="custom-text-noto mb-4">
                  {{ $t($UIConfig.playerInfoCardSetting.dailyListTableText) }}
                </div>
                <dailyList
                  :user-name="selectPlayer.username"
                  :height="breakpoint.xsOnly ? '100%' : tableHeight + 14"
                  :items-per-page="10"
                  :items-per-page-options="[10, 20, 30, -1]"
                  is-card
                ></dailyList>
              </div>
            </v-tab-item>
            <!-- 成就 -->
            <v-tab-item key="achievement" value="achievement">
              <div :class="{ h643: breakpoint.xsOnly }">
                <!-- description -->
                <v-row
                  no-gutters
                  class="custom-text-noto text-caption grey-3--text pb-3"
                  style="font-size: 12px !important"
                >
                  <v-col cols="12">
                    <span>{{ $t('achievements_desc1') }}</span></v-col
                  >
                  <v-col cols="12">
                    <span v-if="$UIConfig.lock.otherStarCityOnlinePlatformsReminderDisabled">{{
                      $t('achievements_desc2_1', { xinstar: $t('daily_list_desc2_2') })
                    }}</span>

                    <i18n v-else path="achievements_desc2_1" tag="span">
                      <template v-slot:xinstar>
                        <span
                          class="primary--text"
                          style="cursor: pointer"
                          @click="openRedirectDialog"
                          >{{ $t('daily_list_desc2_2') }}</span
                        >
                      </template>
                    </i18n>
                  </v-col>
                </v-row>
                <achievement
                  :user-name="selectPlayer.username"
                  :height="breakpoint.xsOnly ? '100%' : tableHeight"
                  :items-per-page="10"
                  :items-per-page-options="[10, 20, 30, -1]"
                  is-card
                ></achievement>
              </div>
            </v-tab-item>
          </v-tabs-items>
        </v-card-text>
      </v-card>
    </v-dialog>
    <remindBindPhoneDialog
      v-if="showRemindBindPhoneDialogStatus"
      :show-remind-bind-phone-dialog-status.sync="showRemindBindPhoneDialogStatus"
    />
  </div>
</template>

<script>
  import relationship from '@/mixins/relationship.js'
  import chat from '@/mixins/chatroom/chat.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import languageOption from '@/mixins/languageOption.js'
  export default {
    name: 'playerInfoCardDialog',
    mixins: [relationship, chat, scssLoader, languageOption],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      dailyList: () => import('~/components/player_info/dailyList'),
      achievement: () => import('~/components/player_info/achievement'),
      characterInfo: () => import('~/components/characterInfo'),
      playerDetail: () => import('~/components/player_info/playerDetail'),
      remindBindPhoneDialog: () => import('~/components/mail/remindBindPhoneDialog.vue')
    },
    props: {
      showPlayerInfoCardDialogStatus: { type: Boolean, required: true, default: false },
      fromLeaderboard: { type: Boolean, default: false }
    },
    data() {
      return {
        showPlayerInfoCardTmp: this.showPlayerInfoCardDialogStatus,
        selectType: 'personal_status',
        friendGrid: {},
        userDetail: {},
        isFriend: false,
        isBlock: false,
        showRemindBindPhoneDialogStatus: false
      }
    },
    created() {
      //使用於是否可以寄信
      this.$store.dispatch('role/updateUserDetail')
      this.$store.dispatch('social/getUserDetail', this.selectPlayer.username).then((res) => {
        this.userDetail = res
      })
    },
    async destroyed() {
      if (this.maintainSystem[0].maintaining) return
    },
    computed: {
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      //由於檢舉功能未上 按鈕隱藏導致高度降低，所以-50
      contentHeight() {
        if (this.selectPlayer.userName === this.userName) {
          return 583
        } else {
          return 685
        }
      },
      tableHeight() {
        if (this.selectPlayer.userName === this.userName) {
          return 334
        } else {
          return 430
        }
      },
      level({ $store }) {
        return $store.getters['role/level']
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      isBind({ $store }) {
        return $store.getters['role/isBind']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      isOpenGameFrame({ $store }) {
        const gameLink = $store.getters['gameHall/gameLink']
        return gameLink !== ''
      },
      vipLevelUpThanBronze({ $store }) {
        return $store.getters['role/vipLevel'] >= 1
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showPlayerInfoCardDialogStatus: {
        async handler(status) {
          if (this.maintainSystem[0].maintaining) {
            return
          }
          this.showPlayerInfoCardDialogStatusTmp = status
        }
      },
      friendList: {
        handler() {
          if (this.checkIsFriend(this.selectPlayer.username)) {
            this.isFriend = true
          } else {
            this.isFriend = false
          }
        },
        deep: true,
        immediate: true
      },
      blockList: {
        handler() {
          if (this.checkIsBlock(this.selectPlayer.username)) {
            this.isBlock = true
          } else {
            this.isBlock = false
          }
        },
        deep: true,
        immediate: true
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showPlayerInfoCardDialogStatus', false)
        this.$emit('update:fromLeaderboard', false)
      },
      openRedirectDialog() {
        this.$nuxt.$emit('root:redirectDialogStatus', { show: true, drawAnalytics: false })
      },
      //寄信
      //判斷流程 是否為正式會員 -> 手機是否有綁定 -> 是否為好友
      openSendMailDialogInDialog(player) {
        const vipLevelSet = this.vipLevel < this.$UIConfig.mailIndex.sendMailLevel
        if (vipLevelSet || this.level === 0) {
          this.$store.dispatch('easyDialog/setDialog', {
            title: this.$t('reminder'),
            message: this.$t(this.$UIConfig.mailIndex.sendNoty)
          })
          this.$nuxt.$emit('root:showNotyDialogStatus', true)
        } else if (!this.isBind) {
          this.showRemindBindPhoneDialogStatus = true
        } else {
          if (!this.checkIsFriend(player.userName)) {
            this.$notify.warning(this.$t('require_friend_to_send_letter'))
          } else {
            this.$nuxt.$emit('root:mailDialogStatus', {
              show: true,
              name: player.username
            })
            this.$store.commit('mail/SET_OPEN_SEND_MAIL_DIRECTLY', true)
            this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', false)
          }
        }
      },
      //加好友
      async checkAddFriendInDialog(username) {
        await this.checkAddFriend(username)
        let friend = this.friendList.find(function (value) {
          return value.username === username
        })
        let friendLowerCase = this.friendList.find(function (value) {
          return value.username.toLowerCase() === username.toLowerCase()
        })
        if (!friend && friendLowerCase) {
          this.$nuxt.$loading.start()
          let role = await this.getPlayerData(friendLowerCase.username)
          this.$nuxt.$loading.finish()
          this.setSelectPlayerInfo(role)
        }
      },
      //密語
      createMessageInDialog(player) {
        if (player.online) {
          this.createMessage(player.userName)
          this.closeDialog()
        } else {
          this.$notify.warning(this.$t('player_offline', { player: player.userName }))
        }
      },
      //加黑名單
      async checkAddBlockInDialog(username) {
        await this.checkAddBlock(username)
        let block = this.blockList.find(function (value) {
          return value.username === username
        })
        let blockLowerCase = this.blockList.find(function (value) {
          return value.username.toLowerCase() === username.toLowerCase()
        })
        if (!block && blockLowerCase) {
          this.$nuxt.$loading.start()
          let role = await this.getPlayerData(blockLowerCase.username)
          this.$nuxt.$loading.finish()
          this.setSelectPlayerInfo(role)
        }
      },
      preventScroll() {
        // 排行榜開啟，阻止切換tab時dialog的滾動行為
        if (!this.fromLeaderboard) return

        const scrollY = window.scrollY || window.pageYOffset

        document.body.style.overflow = 'hidden'
        document.body.style.position = 'fixed'
        document.body.style.width = '100%'
        document.body.style.top = `-${scrollY}px`

        requestAnimationFrame(() => {
          setTimeout(() => {
            document.body.style.overflow = ''
            document.body.style.position = ''
            document.body.style.width = ''
            document.body.style.top = ''
            window.scrollTo(0, scrollY)
          }, 50)
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $primary: map-get($colors, 'primary');
  .border-style-title {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-top: solid 1px $primary-variant-3 !important;
  }
  .border-style-bottom {
    border-left: solid 1px $primary-variant-3 !important;
    border-right: solid 1px $primary-variant-3 !important;
    border-bottom: solid 1px $primary-variant-3 !important;
  }

  .v-tabs-items {
    background-color: transparent !important;
  }
  .a-link {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .w-130-px {
    width: 130px !important;
  }
  .w-173-px {
    width: 173px !important;
  }
  .w-150-px {
    width: 150px !important;
  }
  #player-info-card {
    &.scrollable-sm {
      max-height: calc(90vh - 76px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 76px);
      }
    }
  }
</style>
