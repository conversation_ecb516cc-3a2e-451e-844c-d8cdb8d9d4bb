<template>
  <v-row
    :class="['winners-flags ma-0', { 'pa-0 justify-space-between': isBreakpoint.homeSection.sm }]"
  >
    <template v-if="!isLoading && topPlayersData?.count && !maintainSystem[0].maintaining">
      <!-- xs手機板畫面以輪播圖呈現 -->
      <template v-if="!$vuetify.breakpoint.xs">
        <v-col
          v-for="winnerFlag in setWinnerFlags"
          :key="winnerFlag.playerRankData.username"
          cols="4"
          class="flag pa-0 d-flex align-center h-100-percent"
          :class="`justify-${winnerFlag.position}`"
        >
          <!-- 使用picture回退圖片 -->
          <picture>
            <source :srcset="winnerFlag?.webpSrc" type="image/webp" />
            <img :src="winnerFlag?.pngSrc" height="100%" width="100%" />
          </picture>
          <v-col class="flag-text w-100 py-0 text-center">
            <v-card-text
              @click="openPlayerInfo($event, winnerFlag.playerRankData.username)"
              class="score-player pa-0 pb-1 text-body-1 custom-text-noto text-truncate"
              contenteditable="false"
              >{{ winnerFlag?.playerRankData?.username }}</v-card-text
            >

            <v-card-text class="score-heading pa-0 d-flex justify-center align-center">
              {{ !!activeRank ? $t('win_multiplier') : $t('leaderboard_score') }}
              <div class="rect w-100 d-flex justify-space-between align-center">
                <span class="rect-left"></span>
                <span class="rect-right"></span>
              </div>
            </v-card-text>
            <v-card-text
              class="score-value custom-text-noto pa-0 text-truncate"
              contenteditable="false"
              >{{
                !!activeRank
                  ? (winnerFlag?.playerRankData?.report?.odds).toFixed(2)
                  : winnerFlag?.playerRankData?.report?.winAmount
              }}</v-card-text
            >
            <div class="score-divide w-100 d-flex align-center">
              <div class="line"></div>
              <div class="rect w-100 d-flex justify-space-between align-center">
                <span class="rect-left"></span>
                <span class="rect-right"></span>
              </div>
            </div>
            <v-card-text
              class="pa-0"
              style="cursor: pointer"
              @click="openPlatformGame(winnerFlag.playerRankData)"
            >
              <div>
                <p
                  class="score-platform w-100 ma-0 d-flex justify-center align-center white--text text-no-wrap"
                >
                  {{
                    !!winnerFlag?.playerRankData?.platform
                      ? $t('xincity_platform_web')
                      : $t('xincity')
                  }}
                </p>
                <div class="score-info d-flex justify-start align-center pa-0 pt-1 ma-0">
                  <v-avatar tile>
                    <v-img :src="winnerFlag?.playerRankData?.gameLogoUrl"></v-img>
                  </v-avatar>
                  <span class="text-left text-body-2 custom-text-noto">
                    {{ winnerFlag?.playerRankData?.text }}
                  </span>
                </div>
              </div>
            </v-card-text>
          </v-col>
        </v-col>
      </template>
      <template v-else>
        <leaderboard-winner-swiper
          :set-winner-flags="setWinnerFlags"
          @open-player-info="openPlayerInfo"
          @open-platform-game="openPlatformGame"
        ></leaderboard-winner-swiper>
      </template>
    </template>
    <v-col v-else-if="!isLoading && (maintainSystem[0].maintaining || !topPlayersData?.count)">
      <v-card tile flat style="background: transparent">
        <v-card-text class="winners-flags-empty">
          <p>{{ $t('data_error') }}</p>
          <p>{{ $t('site_maintenance') }}</p></v-card-text
        >
      </v-card>
    </v-col>
  </v-row>
</template>

<script>
  import _ from 'lodash'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import relationship from '~/mixins/relationship.js'
  import analytics from '@/mixins/analytics.js'
  import leaderboard from '~/mixins/leaderboard'
  import timeUtils from '~/mixins/leaderboard/timeUtils.js'

  export default {
    name: 'LeaderboardWinnerFlags',
    components: {
      leaderboardWinnerSwiper: () => import('~/components/leaderboard/winnerSwiper')
    },
    mixins: [preLoginAction, relationship, analytics, leaderboard, timeUtils],
    data() {
      return {}
    },
    computed: {
      setWinnerFlags() {
        if (!this.topPlayersData?.count) return []
        const createFlagImgs = (rank, position, playerRankData) => {
          return {
            webpSrc: require(`~/assets/image/leaderboard/ranking_flag-${rank}_${
              this.$vuetify.breakpoint.width <= 460 ? 'xs' : 'lg'
            }.webp`),
            pngSrc: require(`~/assets/image/leaderboard/ranking_flag-${rank}_${
              this.$vuetify.breakpoint.width <= 460 ? 'xs' : 'lg'
            }.png`),
            position: this.$vuetify.breakpoint.width <= 460 ? 'center' : position,
            playerRankData
          }
        }
        const { list } = this.topPlayersData
        const flag = list.map((item) => {
          return { ...item, ...this.setPlatformGameDetails(item) }
        })

        return [
          createFlagImgs('02', 'start', flag[1]),
          createFlagImgs('01', 'center', flag[0]),
          createFlagImgs('03', 'end', flag[2])
        ]
      }
    },
    created() {
      this.debouncedHandleOpenPlatformGame = _.debounce(this.handleOpenPlatformGame, 300)
      this.debouncedHandleOpenPlayerInfo = _.debounce(this.handleOpenPlayerInfo, 300)
    },
    methods: {
      openPlatformGame(playerRankData) {
        this.debouncedHandleOpenPlatformGame(playerRankData)
      },
      openPlayerInfo(event, username) {
        this.debouncedHandleOpenPlayerInfo(event, username)
      },
      handleOpenPlayerInfo(event, username) {
        this.$emit('open-player-info', event, username)
      },
      handleOpenPlatformGame(playerRankData) {
        this.$emit('open-platform-game', playerRankData)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $default-content: map-get($colors, 'default-content');
  $ranking-winner: map-get($colors, 'ranking-winner');
  $ranking-second: map-get($colors, 'ranking-second-place');
  $ranking-third: map-get($colors, 'ranking-third-place');
  $ranking-winner-50: map-get($colors, 'ranking-winner-opacity-50');
  $ranking-second-50: map-get($colors, 'ranking-second-place-opacity-50');
  $ranking-third-50: map-get($colors, 'ranking-third-place-opacity-50');
  $ranking-fontsize-lg-up: (
    '.score-player': 1vw,
    '.score-heading': 0.75vw,
    '.score-value': 1.25vw,
    '.score-platform': 0.625vw,
    '.score-info span': 0.875vw
  );
  $ranking-fontsize-base: (
    '.score-player': 1rem,
    '.score-heading': 12px,
    '.score-value': 1.25rem,
    '.score-platform': 10px,
    '.score-info span': 0.875rem
  );

  // mixins
  @mixin ranking-color($color, $bg-color) {
    .flag-text {
      .score-player {
        color: $color;
        cursor: pointer;
      }
      .score-heading {
        color: $color;
        .rect {
          &-left,
          &-right {
            background-color: $color;
          }
        }
      }
      .score-heading::before,
      .score-heading::after {
        background-color: $color;
      }
      .score-value {
        color: $color;
      }
      .score-divide {
        background-color: $color;
        .line {
          background-color: $color;
        }
        .rect {
          &-left,
          &-right {
            background-color: $color;
          }
        }
      }
      .score-platform {
        background-color: $bg-color;
      }
      .score-info {
        span {
          color: $color;
        }
      }
    }
  }

  .winners-flags .flag {
    // flags 文字顏色
    &:nth-child(1) {
      @include ranking-color($ranking-second, $ranking-second-50);
    }
    &:nth-child(2) {
      @include ranking-color($ranking-winner, $ranking-winner-50);
    }
    &:nth-child(3) {
      @include ranking-color($ranking-third, $ranking-third-50);
    }

    // flags 文字縮放
    &:nth-child(even) {
      .flag-text {
        @each $selector, $size in $ranking-fontsize-base {
          #{$selector} {
            font-size: calc(#{$size} * 1.05) !important;
          }
        }
      }
    }
    &:nth-child(odd) {
      img,
      picture {
        height: 90% !important;
      }
      .flag-text {
        width: 90%;
      }
      .flag-text {
        @each $selector, $size in $ranking-fontsize-base {
          #{$selector} {
            font-size: calc(#{$size} * 0.95) !important;
          }
        }
      }
    }
  }

  .winners-flags {
    padding: 49.76px 0;

    .flag {
      position: relative;
      aspect-ratio: 0.67 / 1;
      max-width: 32% !important;
      min-height: 200px;
      &:last-child {
        justify-content: flex-end;
      }
      @media screen and (max-width: 460px) {
        aspect-ratio: 0.54/1;
      }

      img,
      picture {
        height: 100%;
        aspect-ratio: 0.67 / 1;
        @media screen and (max-width: 460px) {
          aspect-ratio: 0.54/1;
        }
      }

      &-text {
        position: absolute;
        top: 20%;
        padding: 0 12%;

        .v-card__text {
          line-height: initial !important;
        }
        .score-heading {
          letter-spacing: 0.4px;
          position: relative;

          .rect {
            position: absolute;
            &-left,
            &-right {
              width: 2.5%;
              min-width: 6px;
              aspect-ratio: 2/1;
            }
          }
        }
        .score-heading::before,
        .score-heading::after {
          content: '';
          height: 1px;
          flex-grow: 1;
        }
        .score-heading::before {
          margin-right: 4px;
        }
        .score-heading::after {
          margin-left: 4px;
        }
        .score-value {
          font-weight: 500;
          letter-spacing: 0.25px;
        }
        .score-divide {
          margin-bottom: 8px;
          position: relative;
          height: 1px;
          .line {
            height: 1px;
            width: 100%;
          }
          .rect {
            position: absolute;
            &-left,
            &-right {
              width: 2.5%;
              min-width: 6px;
              aspect-ratio: 2/1;
            }
          }
        }
        .score-platform {
          padding: 2px 0;
          border-radius: 4px 0px;
          p {
            letter-spacing: 0.4px;
          }
        }
        .score-info {
          gap: 8px;
          .v-avatar {
            border-radius: 4px 4px 4px 0px !important;
            max-width: 26.5% !important;
            min-width: 28px !important;
            height: unset !important;
            aspect-ratio: 1 / 1;
          }
          > span {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.5;
          }
        }
      }
    }

    &-empty {
      text-align: center;
      color: $default-content !important;
      font-size: 16px;
      letter-spacing: 0.5px;
    }
  }

  // flags 文字縮放
  @media screen and (min-width: 1400px) {
    .winners-flags {
      .flag {
        &:nth-child(even) {
          .flag-text {
            @each $selector, $size in $ranking-fontsize-lg-up {
              #{$selector} {
                font-size: calc(#{$size} * 1.05) !important;
              }
            }
          }
        }
        &:nth-child(odd) {
          .flag-text {
            @each $selector, $size in $ranking-fontsize-lg-up {
              #{$selector} {
                font-size: calc(#{$size} * 0.95) !important;
              }
            }
          }
        }
      }
    }
  }
</style>
