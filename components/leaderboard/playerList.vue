<template>
  <v-col
    v-if="topPlayersData?.count && !maintainSystem[0].maintaining"
    :cols="isBreakpoint.pageSection.sm ? 12 : isBreakpoint.pageSection.lg ? 7 : 6"
    :style="preventBodyScrollStyle"
  >
    <v-card
      color="transparent"
      flat
      :class="['player', { 'mt-4': !isBreakpoint.pageSection.sm }]"
      @touchstart.passive="handleTouchStart"
      @touchmove="handleTouchMove"
    >
      <v-list
        ref="playerList"
        flat
        class="player-list pa-0 transparent"
        :style="`max-height: ${winnersHeight ?? 500}px;`"
      >
        <template v-for="winner in winnersRankList">
          <v-list-item
            v-observe-visibility="{
              callback: (isVisible) => onVisibilityChange(isVisible, winner?.username),
              throttle: 100
            }"
            :key="winner.username"
            class="player-card py-1 px-0"
            :class="{
              'player-card-active': userPlayerData?.count && winner?.username === userName
            }"
          >
            <div class="player-rank text-body-2 custom-text-noto">
              {{ winner?.sort }}
            </div>
            <v-list-item-content class="player-info px-4 py-0 text-left">
              <v-list-item-title
                @click="openPlayerInfo($event, winner?.username)"
                class="player-name text-body-2 custom-text-noto"
                >{{ winner?.username }}</v-list-item-title
              >
              <v-list-item-subtitle class="player-score">
                {{ isWinsRank ? $t('leaderboard_score') : $t('win_multiplier') }}
                <span
                  class="score-value text-body-2 custom-text-noto gradient-primary--text"
                  contenteditable="false"
                  >{{
                    isWinsRank ? winner?.report?.winAmount : (winner?.report?.odds).toFixed(2)
                  }}</span
                >
              </v-list-item-subtitle>
            </v-list-item-content>
            <div
              class="player-game pl-4 d-flex align-center w-100"
              @click="openPlatformGame(winner)"
            >
              <v-avatar class="game-pic mr-2" size="40" tile>
                <v-img :src="!isLoading ? winner.gameLogoUrl : ''" />
              </v-avatar>
              <div class="game-text">
                <span class="game-tag px-2 mb-1 text-center text-no-wrap" small>{{
                  !!winner?.platform ? $t('xincity_platform_web') : $t('xincity')
                }}</span>
                <div class="game-info mt-1 text-body-2 custom-text-noto text-truncate">
                  {{ !isLoading ? winner.text : '' }}
                </div>
              </div>
            </div>
          </v-list-item>
        </template>
      </v-list>
      <v-card-actions
        v-if="!isLogin"
        class="guide-item d-flex justify-center align-center rounded-0"
      >
        <span class="text-body-2 custom-text-noto mr-4">{{ $t('check_ranking_now') }}</span>
        <v-btn @click="onLoginNow" color="login primary-variant-1 button-content--text px-3">{{
          $t('login_now')
        }}</v-btn>
      </v-card-actions>

      <v-card-actions
        v-else-if="isLogin && !userPlayerData?.count"
        class="guide-item d-flex justify-center align-center rounded-0"
      >
        <span class="text">{{ $t('welcome_leaderboard') }}</span>
      </v-card-actions>
      <v-card-actions
        v-else-if="isLogin && !isUserPlayerVisible"
        class="guide-item d-flex justify-center align-center rounded-0 pa-0"
      >
        <v-list-item class="player-card-active px-0 py-1 w-100">
          <div class="player-rank text-body-2 custom-text-noto text-no-wrap">
            {{ userPlayerData?.list?.sort <= 199 ? userPlayerData?.list?.sort : '199+' }}
          </div>
          <v-list-item-content class="player-info px-4 py-0 text-left">
            <v-list-item-title
              @click="openPlayerInfo($event, userPlayerData?.list?.username)"
              class="player-name text-body-2 custom-text-noto"
              >{{ userPlayerData?.list?.username }}</v-list-item-title
            >
            <v-list-item-subtitle class="player-score">
              {{ isWinsRank ? $t('leaderboard_score') : $t('win_multiplier') }}
              <span class="score-value text-body-2 custom-text-noto gradient-primary--text">{{
                isWinsRank
                  ? userPlayerData?.list?.report?.winAmount
                  : (userPlayerData?.list?.report?.odds).toFixed(2)
              }}</span>
            </v-list-item-subtitle>
          </v-list-item-content>

          <div
            class="player-game pl-4 d-flex align-center w-100"
            @click="openPlatformGame(userPlayerData?.list)"
          >
            <v-avatar class="game-pic mr-2" size="40" tile>
              <v-img :src="userPlayerData?.list?.gameLogoUrl" loading="lazy" />
            </v-avatar>
            <div class="game-text">
              <span class="game-tag px-2 mb-1 text-center text-no-wrap" small>{{
                userPlayerData?.list?.platform ? $t('xincity_platform_web') : $t('xincity')
              }}</span>
              <div class="game-info mt-1 text-body-2 custom-text-noto text-truncate">
                {{ userPlayerData?.list?.text }}
              </div>
            </div>
          </div>
        </v-list-item>
      </v-card-actions>
    </v-card>
  </v-col>
</template>

<script>
  import _ from 'lodash'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import leaderboard from '~/mixins/leaderboard'
  import timeUtils from '~/mixins/leaderboard/timeUtils.js'
  import touchMove from '~/mixins/leaderboard/touchMove.js'

  export default {
    name: 'LeaderboardPlayerList',
    mixins: [preLoginAction, leaderboard, timeUtils, touchMove],
    props: {
      winnersHeight: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        visibleNames: {}
      }
    },
    computed: {
      winnersRankList() {
        if (!this.topPlayersData?.count) return []
        const { list } = this.topPlayersData
        return list.slice(3).map((item) => ({ ...item, ...this.setPlatformGameDetails(item) }))
      },
      isUserPlayerVisible() {
        if (!this.userPlayerData?.count) return false

        const userSort = this.userPlayerData.list.sort
        if (userSort > 200) return false

        const username = this.userPlayerData.list.username
        return Boolean(this.visibleNames[username])
      }
    },
    created() {
      this.debouncedHandleOpenPlatformGame = _.debounce(this.handleOpenPlatformGame, 300)
      this.debouncedHandleOpenPlayerInfo = _.debounce(this.handleOpenPlayerInfo, 300)
    },

    methods: {
      onVisibilityChange(isVisible, name) {
        if (!this.isLogin || !name) return
        if (
          !this.userPlayerData?.count ||
          this.userPlayerData?.list?.sort <= 3 ||
          this.userPlayerData?.list?.sort > 200
        )
          return
        // 只在可見性狀態變化時更新
        const wasVisible = this.visibleNames[name] || false

        if (isVisible !== wasVisible) {
          this.$set(this.visibleNames, name, isVisible)
        }
      },
      scrollToTop() {
        if (!this.playerListEl) {
          this.playerListEl = this.$refs?.playerList?.$el
        }
        this.playerListEl.scrollTop = 0
      },
      resetVisibleNames() {
        this.visibleNames = {}
      },
      onLoginNow() {
        this.setPreLoginAction()
        this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
      },
      openPlayerInfo(event, username) {
        this.debouncedHandleOpenPlayerInfo(event, username)
      },
      openPlatformGame(playerRankData) {
        this.debouncedHandleOpenPlatformGame(playerRankData)
      },
      handleOpenPlatformGame(playerRankData) {
        this.$emit('open-platform-game', playerRankData)
      },
      handleOpenPlayerInfo(event, username) {
        this.$emit('open-player-info', event, username)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $tr-border: map-get($colors, 'player-ranking-tr-border');
  $player-ranking-border: linear-gradient(
      270deg,
      rgba(255, 190, 142, 0) 0%,
      #ffbe8e 50%,
      rgba(255, 190, 142, 0) 100%
    )
    1;

  .player {
    margin-left: 20px;
    position: relative;
    border-top: 2px solid;
    border-bottom: 2px solid;
    border-image: $player-ranking-border;

    @media screen and (min-width: 1400px) {
      margin-left: 4vw;
    }
    @media screen and (max-width: 780px) {
      margin-left: 0;
    }
    .guide-item {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 49px;
      z-index: 2;
      background: rgba(26, 11, 11, 0.8);
      .text {
        font-size: 12px;
        letter-spacing: 0.4px;
      }
      .login {
        font-size: 12px;
        letter-spacing: 1.071px;
      }
    }
    &::before,
    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      height: 100px;
      pointer-events: none;
      z-index: 1;
    }
    &::before {
      top: 0;
      background: radial-gradient(
        ellipse 70% 70% at center top,
        rgb(114 70 41 / 20%) 0%,
        rgb(94 62 40 / 30%) 50%,
        rgba(26, 11, 11, 0%) 100%
      );
    }
    &::after {
      bottom: 0;
      background: radial-gradient(
        ellipse 80% 100% at center bottom,
        rgb(114 70 41 / 20%) 0%,
        rgb(94 62 40 / 30%) 50%,
        rgba(26, 11, 11, 0%) 100%
      );
    }
    &-list {
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;

      @media screen and (max-width: 959px) {
        // Safari 特定優化
        -webkit-overflow-scrolling: touch;
        touch-action: pan-y;
        overscroll-behavior-y: contain;
      }
    }
    &-card {
      position: relative;
      background-color: #2d1013;
      display: flex;
      align-items: center;
      border-bottom: 1px solid $tr-border;

      &:last-child {
        padding-bottom: 57px !important;
      }
      &-active {
        background: linear-gradient(
            270deg,
            rgba(165, 114, 89, 0) 0%,
            #a57259 50%,
            rgba(165, 114, 89, 0.3) 100%
          ),
          #241010;
      }
    }
    &-rank {
      padding: 0 16px;
      min-width: 48px;
      max-width: 48px;
      white-space: nowrap;
    }
    &-info {
      min-width: 136px;
      max-width: 136px;
      z-index: 1;
      .player-name {
        cursor: pointer;
      }
    }
    &-score {
      color: rgba(255, 255, 255, 0.5);
      font-size: 12px;
      letter-spacing: 0.4px;
      overflow: visible;
      text-overflow: unset;
    }
    &-game {
      z-index: 1;
      overflow: hidden;
      cursor: pointer;
      .game-pic {
        border-radius: 4px 4px 4px 0px !important;
        overflow: hidden;
      }
      .game-text {
        min-width: 0;
      }
      .game-tag {
        padding-top: 2px;
        padding-bottom: 2px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px 0px;
        font-size: 10px;
        width: 70px;
        letter-spacing: 0.4px;
        line-height: 1;
      }
    }
  }
</style>
