<template>
  <v-dialog
    v-model="showTobeFormalDialogStatus"
    persistent
    max-width="380"
    transition="dialog-transition"
    content-class="rounded-lg"
  >
    <v-card color="dialog-fill" class="pa-4 pa-sm-6">
      <v-card-title
        class="custom-text-noto text-h6 font-weight-regular grey-1--text justify-center pa-0"
      >
        {{ $t('reminder').toUpperCase() }}
      </v-card-title>
      <v-card-text class="px-0 py-6">
        <div class="custom-text-noto text-body-2 default-content--text">
          {{ $t('coffer_desc1') }}
        </div>
        <div class="custom-text-noto text-body-2 default-content--text">
          {{ $t('coffer_desc2') }}
        </div>
      </v-card-text>
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
            ><v-btn
              text
              @click="closeDialog"
              class="w-100"
              :class="breakpoint.xsOnly ? 'w-100' : ''"
            >
              <span class="custom-text-noto default-content--text">
                {{ $t('latter').toUpperCase() }}
              </span>
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              depressed
              @click="goToGiftPack"
            >
              {{ $t('upgrade_real').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  export default {
    name: 'toBeFormalDialog',
    mixins: [hiddenScrollHtml],
    props: {
      showTobeFormalDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showTobeFormalDialogStatusTmp: this.showTobeFormalDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showTobeFormalDialogStatus', false)
      },
      goToGiftPack() {
        this.closeDialog()
        this.$emit('goToGiftPack')
      }
    }
  }
</script>
