<template>
  <div>
    <v-dialog
      v-model="showRedeemDialogStatusTmp"
      max-width="400px"
      persistent
      content-class="rounded-lg"
    >
      <v-card tile color="bg-dialog">
        <v-card-title
          class="custom-text-noto text-subtitle-1 justify-center gradient-primary-left button-content--text bg-dialog-header text-dialog-header-inverse--text"
        >
          {{ $t('serial_redeem') }}
        </v-card-title>
        <v-card-text class="pb-0 px-4 pt-4 px-sm-6 pt-sm-6">
          <v-text-field
            v-model="cardSerialNumber"
            shaped
            data-vv-scope="redeemGroup"
            v-validate="{ required: true, redeem_code_limit: true }"
            @keydown.enter="sendRedeemCodeEvent(cardSerialNumber)"
            :error-messages="errors.first('redeemGroup.serial_number')"
            :placeholder="$t('please_enter_serial')"
            :label="$t('serial_number') + '*'"
            class="input-height"
            name="serial_number"
            maxlength="32"
            filled
          />
        </v-card-text>
        <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
          <v-row no-gutters justify="end">
            <v-col class="pr-2" :cols="breakpoint.xsOnly ? '6' : 'auto'"
              ><v-btn
                :class="['default-content--text btn-soft--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="closeDialog"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col class="pl-2" :cols="breakpoint.xsOnly ? '6' : 'auto'"
              ><v-btn
                :class="['button-content--text text-btn--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.replaceColor.bgBtn"
                depressed
                :disabled="disabledSubmit"
                @click="sendRedeemCodeEvent(cardSerialNumber)"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="showRedeemSuccessStatus" persistent max-width="400px">
      <v-card elevation="0" tile color="bg-dialog" class="pb-2">
        <v-card-title class="custom-text-noto text-h6 justify-center text-dialog-header--text">
          {{ $t('hint') }}
        </v-card-title>
        <v-card-text class="default-content--text py-4 pa-5 text-regular--text">
          <span class="redeem-font-style">{{ showMsg }}</span>
          <br />
          <span v-show="showDownloadRow">
            <span v-if="$UIConfig.lock.otherStarCityOnlinePlatformsReminderDisabled">
              <i18n path="redeem_success_noty2">
                <template v-slot:mark>＊</template>
                <template v-slot:download>{{ $t('go_download') }}</template>
              </i18n>
            </span>
            <span v-else>
              <i18n path="redeem_success_noty2">
                <template v-slot:mark>＊</template>
                <template v-slot:download>
                  <a
                    class="text-decoration-underline text-medium--text"
                    @click="openRedirectDialog"
                    >{{ $t('go_download') }}</a
                  >
                </template>
              </i18n>
            </span>
          </span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />

          <v-btn
            class="mx-2 button-content--text text-btn--text"
            :color="$UIConfig.replaceColor.bgBtn"
            depressed
            @click="doSure"
          >
            {{ $t('sure').toUpperCase() }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
  import { isNullOrEmpty } from '@/utils/stringUtils'
  import analytics from '@/mixins/analytics.js'
  import redeemMgr from '@/mixins/redeemMgr.js'
  export default {
    name: 'ShowRedeemDialog',
    mixins: [analytics, redeemMgr],
    props: {
      showRedeemDialogStatus: { type: Boolean, default: false }
    },
    data() {
      const errorKey = ['門號認證']
      const redeemKey = ['星幣', '銀幣']
      const starCoin = ['星幣']
      const officialMemberKey = ['成為正式會員']
      const systemMessageType = 0
      return {
        errorKey,
        redeemKey,
        starCoin,
        officialMemberKey,
        systemMessageType,
        disabledSubmit: true,
        showDownloadRow: false,
        showRedeemDialogStatusTmp: this.showRedeemDialogStatus,
        showRedeemSuccessStatus: false,
        cardSerialNumber: '',
        requestMsg: '',
        showMsg: ''
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showRedeemDialogStatus: {
        async handler(value) {
          this.showRedeemDialogStatusTmp = value
        }
      },
      cardSerialNumber: {
        async handler() {
          const validateFormName = 'redeemGroup.*'
          const validate = await this.$validator.validate(validateFormName)
          this.disabledSubmit = !validate
        },
        deep: true
      }
    },
    async created() {},
    async beforeDestroy() {},
    methods: {
      removeDash: (str) => (str.startsWith('-') ? str.slice(1) : str),
      showRedeemRequest(request) {
        switch (request.type) {
          case 0:
            {
              this.requestMsg = isNullOrEmpty(request.message)
                ? ''
                : this.removeDash(request.message)
              if (
                this.checkItemMatch(this.requestMsg, this.redeemKey) ||
                this.checkItemMatch(this.requestMsg, this.officialMemberKey)
              ) {
                const showRow =
                  !this.checkItemMatch(this.requestMsg, this.starCoin) &&
                  !this.checkItemMatch(this.requestMsg, this.officialMemberKey)
                this.onRedeemSuccess(this.getShowMsg(), showRow)
                this.updateUserData()
                this.resetFieldText()
              } else {
                this.$notify.error(this.requestMsg)
              }
            }
            break
          case 11:
            {
              const redeemString = this.$t('redeem_success_noty1', {
                item: this.$t('redeem_coupon')
              })
              switch (request.kind) {
                case 0:
                  this.$notify.error(this.$t('top_up_fail2'))
                  break /** 卡號或密碼錯誤(21041) */
                case 1:
                  this.$notify.error(this.$t('top_up_fail3'))
                  break /** 此卡已被儲值(21042) */
                case 2:
                  this.$notify.error(this.$t('redeem_fail'))
                  break /** 使用對象錯誤(21043) */
                case 3:
                  this.$notify.error(this.$t('redeem_fail1'))
                  break /** 使用期限超過(21044) */
                case 4:
                  this.$notify.error(this.$t('redeem_fail2'))
                  break /** 資料錯誤(21045) */
                case 5:
                  this.onRedeemSuccess(redeemString, true)
                  this.resetFieldText()
                  break /** 兌獎完成(21046) */
                default:
                  this.$notify.error(this.$t('redeem_data_error'))
                  break /** 序號兌換回傳值，無法辨識！ */
              }
            }
            break
          case 187:
            {
              const redeemString = this.$t('redeem_success_noty1', {
                item: this.$t('redeem_coupon')
              })
              this.onRedeemSuccess(redeemString, true)
            }
            break
          default:
            this.$notify.error(this.$t('redeem_data_error'))
            break
        }
      },
      async onRedeemSuccess(message, showDownloadRow) {
        this.showMsg = message
        this.showDownloadRow = showDownloadRow
        this.showRedeemDialogStatusTmp = false
        this.showRedeemSuccessStatus = true
      },
      updateUserData() {
        setTimeout(() => {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())
          //首儲更新玩家資料
          if (this.checkItemMatch(this.requestMsg, this.officialMemberKey))
            this.$store.commit('role/SET_VIPLEVEL', 1)
        }, 100)
      },

      resetFieldText() {
        this.$validator.reset('form.*')
        this.disabledSubmit = true
        this.cardSerialNumber = ''
      },
      closeDialog() {
        this.resetFieldText()
        this.$nuxt.$emit('root:showRedeemDialogStatus', false)
      },
      async sendRedeemCodeEvent(redeemCode) {
        const request = await this.redeemCodeRequest(redeemCode)
        if (request) this.showRedeemRequest(request)
      },
      getShowMsg() {
        if (this.checkItemMatch(this.requestMsg, this.officialMemberKey))
          return this.$t('redeem_success_noty')

        return this.$t('redeem_success_noty1', { item: this.getSlicString(this.requestMsg, ' ') })
      },
      openRedirectDialog() {
        this.$nuxt.$emit('root:redirectDialogStatus', { show: true, drawAnalytics: false })
      },
      getSlicString(message, sliceKey) {
        const messageItemIdx = message.indexOf(sliceKey) + 1
        const returnMessage = this.formatNumberWithCommas(message.slice(messageItemIdx))
        return returnMessage
      },
      checkItemMatch(message, keys) {
        return keys.some((key) => message.includes(key))
      },
      formatNumberWithCommas(text) {
        const numbersOnly = text.replace(/[^\d]/g, '')
        const formattedNumber = numbersOnly.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        return text.replace(/\d+/g, formattedNumber)
      },
      doSure() {
        this.showDownloadRow = false
        this.showRedeemSuccessStatus = false
        this.$nuxt.$emit('root:showRedeemDialogStatus', false)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .redeem-font-style {
    white-space: pre-wrap;
  }
</style>
