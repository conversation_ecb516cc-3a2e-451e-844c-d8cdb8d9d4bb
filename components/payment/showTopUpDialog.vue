<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showTopUpDialogStatusTmp"
    max-width="400px"
    persistent
    content-class="rounded-lg"
  >
    <v-card tile color="dialog-fill">
      <v-card-title
        class="custom-text-noto text-subtitle-1 justify-center gradient-primary-left button-content--text bg-dialog-header text-dialog-header-inverse--text"
      >
        {{ $t(selectedProvider.dict) + $t('stored_value') }}
      </v-card-title>
      <v-form>
        <v-card-text class="pb-0 px-4 pt-4 px-sm-6 pt-sm-6">
          <v-text-field
            v-show="showPaymentGroup1Status"
            v-model="card.cardNumber"
            v-validate="{ required: true, is_number_card: true }"
            type="text"
            name="card_number"
            :error-messages="errors.first('paymentGroup1.card_number')"
            :label="$t('card_number') + '*'"
            data-vv-scope="paymentGroup1"
            filled
            shaped
          />
          <v-text-field
            v-show="showPaymentGroup1Status"
            v-model="card.cardPwd"
            v-validate="{ required: true, card_number_limit: true }"
            type="text"
            name="password"
            :error-messages="errors.first('paymentGroup1.password')"
            :label="$t('password') + '*'"
            data-vv-scope="paymentGroup1"
            filled
            shaped
          />
          <v-text-field
            v-show="showPaymentGroup2Status"
            v-model="card.cardSerialNumber"
            v-validate="{ required: true, card_number_limit: true }"
            type="text"
            name="serial_number"
            :error-messages="errors.first('paymentGroup2.serial_number')"
            :label="$t('serial_number') + '*'"
            filled
            shaped
            data-vv-scope="paymentGroup2"
          />
          <v-text-field
            v-show="showPaymentGroup3Status"
            v-model="card.cardGashPwd"
            v-validate="{ required: true, number21_limit: true }"
            type="text"
            name="cardGashPwd"
            :error-messages="errors.first('paymentGroup3.cardGashPwd')"
            :label="$t('password') + '*'"
            filled
            shaped
            data-vv-scope="paymentGroup3"
          />
        </v-card-text>
        <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
              ><v-btn
                :class="['default-content--text btn-soft--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="closeDialog"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn></v-col
            >
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text text-btn--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.replaceColor.bgBtn"
                depressed
                :disabled="disabledSubmit"
                @click="doPrepare(selectedProvider.id)"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn></v-col
            >
          </v-row>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import payment from '@/mixins/payment.js'
  const STATION = process.env.STATION
  export default {
    name: 'ShowTopUpDialog',
    mixins: [analytics, payment],
    props: {
      showTopUpDialogStatus: { type: Boolean, default: false },
      showTopUpConfirmDialog: { type: Boolean, default: false },
      selectedProvider: { type: Object, default: () => {} }
    },
    data() {
      const card = {
        cardNumber: '',
        cardPwd: '',
        cardSerialNumber: '',
        cardGashPwd: ''
      }
      return {
        card,
        disabledSubmit: true,
        showPaymentGroup1Status: false,
        showPaymentGroup2Status: false,
        showPaymentGroup3Status: false,
        showTopUpDialogStatusTmp: this.showTopUpDialogStatus
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      'card.cardNumber'(value) {
        if (/[a-z]/.test(value)) {
          this.card['cardNumber'] = value.toUpperCase()
        }
      },
      'card.cardPwd'(value) {
        if (/[a-z]/.test(value)) {
          this.card['cardPwd'] = value.toUpperCase()
        }
      },
      'card.cardSerialNumber'(value) {
        if (/[a-z]/.test(value)) {
          this.card['cardSerialNumber'] = value.toUpperCase()
        }
      },
      'card.cardGashPwd'(value) {
        if (/[a-z]/.test(value)) {
          this.card['cardGashPwd'] = value.toUpperCase()
        }
      },
      card: {
        async handler() {
          let validateFormName = ''
          if (this.showPaymentGroup1Status) {
            validateFormName = 'paymentGroup1.*'
          } else if (this.showPaymentGroup2Status) {
            validateFormName = 'paymentGroup2.*'
          } else if (this.showPaymentGroup3Status) {
            validateFormName = 'paymentGroup3.*'
          }
          if (validateFormName !== '') {
            const validate = await this.$validator.validate(validateFormName)
            if (validate) {
              this.disabledSubmit = false
            } else {
              this.disabledSubmit = true
            }
          }
        },
        deep: true
      },
      showTopUpDialogStatus: {
        handler(status) {
          this.$store.commit(`${STATION}/payment/CLEAR_STOREDPOINT`)
          this.showTopUpDialogStatusTmp = status
          this.showPaymentField(this.selectedProvider.id, status)
        }
      }
    },
    methods: {
      async showPaymentField(paymentProviderId, status) {
        switch (paymentProviderId) {
          case 1:
            this.showPaymentGroup1Status = status
            break
          case 2:
            this.showPaymentGroup2Status = status
            break
          case 3:
            this.showPaymentGroup3Status = status
            break
          default:
        }
      },
      closeDialog() {
        this.$validator.reset('form.*')

        this.card = {
          cardNumber: '',
          cardPwd: '',
          cardSerialNumber: '',
          cardGashPwd: ''
        }

        this.showPaymentGroup1Status = false
        this.showPaymentGroup2Status = false
        this.showPaymentGroup3Status = false
        this.disabledSubmit = true
        this.showTopUpDialogStatusTmp = false

        this.$emit('update:showTopUpDialogStatus', false)
      },
      async doPrepare(paymentProviderId) {
        const validate = await this.$validator.validate('form.*')
        let analyticsStatus = false

        if (validate) {
          await this.bankInit()

          const cardNumber = this.card.cardNumber.trim()
          const cardPwd = this.card.cardPwd.trim()
          const cardGashPwd = this.card.cardGashPwd.trim()

          const cardSerialNumber = this.card.cardSerialNumber.trim()
          let req = {}
          let res = []
          let message = ''
          this.disabledSubmit = true
          try {
            let isYoe = true
            switch (paymentProviderId) {
              case 1:
                this.$wsClient.send(this.$wsPacketFactory.verifyYoeCard(cardNumber, cardPwd))
                res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
                  return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.VERIFY)
                })
                req = {
                  cardNumber,
                  cardPwd
                }
                break
              case 2:
                this.$wsClient.send(this.$wsPacketFactory.verifyDigitalYoeCard(cardSerialNumber))
                res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
                  return data.isFeature(this.$xinConfig.FEATURE.BANK.TYPE.VERIFY)
                })
                req = {
                  cardSerialNumber
                }
                break
              case 3:
                {
                  req = {
                    cardGashPwd
                  }
                  isYoe = false
                }
                break

              default:
                break
            }
            if (Object.prototype.hasOwnProperty.call(res, 'code')) {
              if (res.code === 361) {
                message = this.$t('top_up_fail2')
              } else if (res.code === 362) {
                message = this.$t('top_up_fail3')
              } else if (res.code === 197) {
                message = this.$t('coed_or_password_error_much')
              }
              // this.showNotyDialog(title, message)
              this.$notify.error(message)
              this.disabledSubmit = false
            } else if (!isYoe) {
              const confirmData = {
                title: this.$t('reminder'),
                message: this.$t('gash_message', { cardSerialNumber: cardGashPwd }),
                btnText: this.$t('sure').toUpperCase(),
                data: {
                  paymentProviderId,
                  req
                }
              }
              this.$store.commit(`${STATION}/payment/SET_CONFIRMDATA`, confirmData)
              this.$emit('update:showTopUpConfirmDialogStatus', true)
              this.closeDialog()
              analyticsStatus = true
            } else if (
              Object.prototype.hasOwnProperty.call(res, 'Get_OTP') &&
              res.Get_OTP === null
            ) {
              this.$notify.error(res.RetStr)
              this.disabledSubmit = false
            } else if (
              Object.prototype.hasOwnProperty.call(res, 'Get_OTP') &&
              res.Get_OTP !== null
            ) {
              req.price = res.Get_Res.Price
              req.point = res.Get_CPPoint
              const confirmData = {
                title: this.$t('confirmed_store_value_noty1'),
                message: this.$t('yoe_card'),
                btnText: this.$t('sure').toUpperCase(),
                data: {
                  paymentProviderId,
                  req
                }
              }
              this.$store.commit(`${STATION}/payment/SET_CONFIRMDATA`, confirmData)
              this.$emit('update:showTopUpConfirmDialogStatus', true)
              this.closeDialog()
              analyticsStatus = true
            } else if (res.isSuccess) {
              const confirmData = {
                title: this.$t('confirmed_store_value_noty1'),
                message: res.message, // 回傳吃SERVER的，需要確認多語系
                btnText: this.$t('sure').toUpperCase(),
                data: {
                  paymentProviderId,
                  req
                }
              }
              this.$store.commit(`${STATION}/payment/SET_CONFIRMDATA`, confirmData)

              this.$emit('update:showTopUpConfirmDialogStatus', true)
              this.closeDialog()
              analyticsStatus = true
            } else {
              this.$notify.error(this.$t('top_up_fail1'))
            }

            this.$validator.reset('form.*')
            if (analyticsStatus) {
              let label = ''
              switch (paymentProviderId) {
                case 1:
                  label = 'Webyoeqpp'
                  break
                case 2:
                  label = 'Webyoe'
                  break
                case 3:
                  label = 'Webgash'
                  break
                case 4:
                  label = 'Webcreditcard'
                  break
                case 5:
                  label = 'Weboverseas'
                  break

                default:
                  break
              }
              this.paymentClickAnalytics(label)
            }
          } catch (error) {
            console.log(error)
          }
        }
      }
    }
  }
</script>
