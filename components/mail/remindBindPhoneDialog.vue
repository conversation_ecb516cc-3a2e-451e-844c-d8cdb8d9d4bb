<template>
  <v-dialog
    v-model="showRemindBindPhoneDialogStatusTmp"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card color="transparent" class="pa-4 pa-sm-6">
      <v-card-title class="custom-text-noto text-h6 grey-1--text justify-center pa-0">
        {{ $t('reminder') }}
      </v-card-title>
      <v-card-text class="custom-text-noto text-body-2 default-content--text px-0 py-6">
        {{ $t('mobile_binding_hint') }}
      </v-card-text>
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
            <v-btn
              class=""
              :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="latterBindPhone"
            >
              {{ $t('latter').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :color="$UIConfig.defaultBtnColor"
              depressed
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              @click="goBindPhone"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'MailConfirmDeleteDialog',
    props: {
      showRemindBindPhoneDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showRemindBindPhoneDialogStatusTmp: this.showRemindBindPhoneDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showRemindBindPhoneDialogStatus: function (val) {
        this.showRemindBindPhoneDialogStatusTmp = val
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showRemindBindPhoneDialogStatus', false)
      },
      goBindPhone() {
        this.closeDialog()
        this.$nuxt.$emit('root:showPhoneNumBindingDialogStatus', true)
      },
      latterBindPhone() {
        this.closeDialog()
        this.$store.dispatch('easyDialog/setDialog', {
          title: this.$t('reminder'),
          message: this.$t('in_player_status_bind_phone_num_dialog')
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      }
    }
  }
</script>

<style></style>
