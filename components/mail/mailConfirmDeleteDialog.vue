<template>
  <v-row justify="center">
    <v-dialog
      v-model="confirmDeletionStatusTmp"
      persistent
      max-width="290"
      content-class="rounded-lg"
    >
      <v-card color="transparent" class="pa-4 pa-sm-6">
        <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
          {{ $t('reminder') }}
        </v-card-title>
        <v-card-text class="default-content--text px-0 py-6">
          {{ $t('confirm_deletion_warning3') }}
        </v-card-text>
        <v-card-actions class="pa-0">
          <v-row no-gutters justify="end">
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
              <v-btn
                :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                text
                @click="closeDialog"
              >
                {{ $t('cancel').toUpperCase() }}
              </v-btn>
            </v-col>
            <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
              <v-btn
                :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                :color="$UIConfig.defaultBtnColor"
                depressed
                @click="doDeleteRead"
              >
                {{ $t('sure').toUpperCase() }}
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>
  export default {
    name: 'MailConfirmDeleteDialog',
    props: {
      confirmDeletionStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        confirmDeletionStatusTmp: this.confirmDeletionStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      confirmDeletionStatus: function (val) {
        this.confirmDeletionStatusTmp = val
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:confirmDeletionStatus', false)
      },
      doDeleteRead() {
        this.$emit('doDeleteRead')
      }
    }
  }
</script>

<style></style>
