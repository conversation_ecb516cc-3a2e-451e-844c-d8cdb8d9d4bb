<template>
  <v-dialog
    :value="showConfirmGuildInviteDialogStatusTmp"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card color="transparent" class="pa-4 pa-sm-6">
      <v-card-title class="custom-text-noto text-h6 grey-1--text justify-center pa-0">
        {{ $t('reminder').toUpperCase() }}
      </v-card-title>
      <v-card-text class="default-content--text text-body-2 custom-text-noto px-0 py-6">
        <template v-if="isAccept">
          <div class="default-content--text">
            {{ $t('guild_add_noty', { guild: getSingleMail.from }) }}
          </div>
          <div class="warning--text">{{ $t('guild_free_leave_noty') }}</div>
        </template>
        <div v-else class="default-content--text">
          {{ $t('guild_decline_noty', { guild: getSingleMail.from }) }}
        </div>
      </v-card-text>
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
            <v-btn
              :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="closeDialog"
            >
              {{ $t('cancel').toUpperCase() }}
            </v-btn>
          </v-col>
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :color="$UIConfig.defaultBtnColor"
              depressed
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              @click="replyInvitationLetter"
            >
              {{ isAccept ? $t('join').toUpperCase() : $t('decline').toUpperCase() }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'confirmGuildInviteDialog',
    props: {
      showConfirmGuildInviteDialogStatus: {
        type: Boolean,
        default: false
      },
      getSingleMail: {
        type: Object,
        default: () => ({})
      },
      isAccept: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {}
    },
    computed: {
      showConfirmGuildInviteDialogStatusTmp() {
        return this.showConfirmGuildInviteDialogStatus
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showConfirmGuildInviteDialogStatus', false)
      },
      replyInvitationLetter() {
        this.closeDialog()
        this.$emit('replyInviteLetter', this.getSingleMail.mailId, this.isAccept)
      }
    }
  }
</script>

<style></style>
