<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showNotyNotBindPhoneDialogStatusTmp"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card color="transparent" class="pa-4 pa-sm-6">
      <v-card-title
        class="custom-text-noto text-h6 justify-center font-weight-regular grey-1--text pa-0"
        v-text="$t('reminder')"
      />
      <v-card-text
        class="default-content--text px-0 py-6"
        v-text="$t('personal_info_phone_number_tooltip')"
      />
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
            ><v-btn
              :class="['custom-text-noto default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="showLaterNoty()"
            >
              {{ $t('latter').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              depressed
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              @click="showPhoneNumBindingDialog()"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import preLoginAction from '~/mixins/preLoginAction'
  export default {
    name: 'NotyNotBindPhoneDialog',
    mixins: [hiddenScrollHtml, preLoginAction],
    props: {
      showNotyNotBindPhoneDialogStatus: { type: Boolean, default: true }
    },
    data() {
      return {
        showNotyNotBindPhoneDialogStatusTmp: this.showNotyNotBindPhoneDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      },
      preLoginAction({ $store }) {
        return $store.getters['role/preLoginAction']
      }
    },
    watch: {
      showNotyNotBindPhoneDialogStatus: {
        handler(status) {
          this.showNotyNotBindPhoneDialogStatusTmp = status
        }
      }
    },
    methods: {
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      closeDialog() {
        this.$nuxt.$emit('root:showNotyNotBindPhoneDialogStatus', false)
        // 若登入前點選公會選單，則改變公會選單狀態為active
        if (this.preLoginAction.name === 'dropdown') {
          this.setGuildCategoryActive()
        }
      },
      showLaterNoty() {
        this.closeDialog()
        this.showNotyDialog(this.$t('reminder'), this.$t('in_player_status_bind_phone_num_dialog'))
      },
      showPhoneNumBindingDialog() {
        this.closeDialog()
        this.$nuxt.$emit('root:showPhoneNumBindingDialogStatus', true)
      }
    }
  }
</script>
