<template>
  <v-dialog
    v-model="showMusicPlayerDialogStatusTemp"
    persistent
    max-width="460"
    content-class="rounded-lg"
  >
    <v-card class="dialog-fill bg-dialog">
      <customDialogTitle :title="$t('background_music').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text class="pa-4 pa-sm-6">
        <v-container fluid class="pa-0">
          <v-row no-gutters>
            <v-select
              v-model="selectedMusic"
              filled
              shaped
              dense
              height="40px"
              item-text="title"
              :items="musicList"
              :label="$t('background_music')"
              item-color="primary"
              item-value="value"
            />
          </v-row>
          <v-row no-gutters>
            <v-col cols="12">
              <span
                class="default-content--text text-subtitle-2 custom-text-noto text-regular--text"
                >{{ $t('lobby_volume') }}</span
              >
            </v-col>
            <v-col cols="12" class="mt-4">
              <v-slider
                hide-details
                dense
                :max="volMax"
                :min="volMin"
                :color="$UIConfig.replaceColor.bgSliderTrack"
                :track-color="$UIConfig.replaceColor.bgSlider"
                :thumb-color="$UIConfig.replaceColor.bgSliderThumb"
                v-model="volume"
                id="music-slider"
                class="music-slider-container"
                @mousedown="setLastVolume(volume)"
              >
                <template v-slot:prepend>
                  <v-hover v-slot="{ hover }">
                    <v-btn
                      icon
                      @click="setNowVolume()"
                      @mousedown="volume !== 0 && setLastVolume(volume)"
                    >
                      <v-icon
                        :class="[
                          needHover,
                          'material-symbols-outlined default-content--text text-slider--text'
                        ]"
                      >
                        {{ getVolumeIcon(hover) }}
                      </v-icon>
                    </v-btn>
                  </v-hover>
                </template>
                <template v-slot:append>
                  <span class="body-2 default-content--text volume-percentage text-slider--text"
                    >{{ volume }}%</span
                  >
                </template>
              </v-slider>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  import throttle from 'lodash/throttle'
  import debounce from 'lodash/debounce'
  import music from '~/mixins/music'

  export default {
    name: 'musicPlayerDialog',
    mixins: [music],
    props: {
      showMusicPlayerDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    data() {
      return {
        showMusicPlayerDialogStatusTemp: false,
        selectedMusic: -1,
        volMax: 100,
        volMin: 0,
        touchableDevice: true
      }
    },
    created() {
      // 創建localStorage的防抖
      this.debouncedSaveMusicPreference = debounce(this.saveMusicPreferenceToLocalStorage, 300)
      // 創建音量節流
      this.throttledSetVolume = throttle((val) => {
        this.$store.commit('music/SET_VOLUME', val)
      }, 50)
    },
    beforeDestroy() {
      // 取消防抖
      if (this.debouncedSaveMusicPreference && this.debouncedSaveMusicPreference.cancel) {
        this.debouncedSaveMusicPreference.cancel()
      }
      // 取消節流
      if (this.throttledSetVolume?.cancel) {
        this.throttledSetVolume.cancel()
      }
    },
    computed: {
      isPlaying({ $store }) {
        return $store.getters['music/isPlaying']
      },
      tracks({ $store }) {
        return $store.getters['music/tracks']
      },
      currentTrack({ $store }) {
        return $store.getters['music/currentTrack']
      },
      musicList() {
        let locale = this.$i18n.locale
        let musicMap = [
          { title: this.$t('play_in_order'), value: -1 },
          ...this.tracks.map((track, index) => ({
            title: track.name[locale],
            src: track.src,
            value: index
          }))
        ]
        return musicMap
      },
      volume: {
        get() {
          return this.$store.getters['music/volume']
        },
        set(val) {
          // 使用節流更新音量
          this.throttledSetVolume(val)
          this.debouncedSaveMusicPreference()
        }
      },
      isPlayByOrder({ $store }) {
        return $store.getters['music/isPlayByOrder']
      },
      lastVolume({ $store }) {
        return $store.getters['music/lastVolume']
      },
      needHover() {
        return !this.touchableDevice && 'needHover'
      }
    },
    watch: {
      showMusicPlayerDialogStatus: {
        immediate: true,
        handler(val) {
          this.showMusicPlayerDialogStatusTemp = val
        }
      },
      selectedMusic: {
        handler(val) {
          if (val === -1) {
            this.$store.commit('music/SET_IS_PLAY_BY_ORDER', true)
          } else {
            this.$store.commit('music/SET_IS_PLAY_BY_ORDER', false)
            this.$store.commit('music/SET_CURRENT_TRACK', val)
          }
          this.saveMusicPreferenceToLocalStorage()
        }
      },
      lastVolume: {
        handler(val) {
          // 如果音量、lastVolume為0，則將lastVolume設置為50
          if (this.volume === 0 && val === 0) {
            this.$store.commit('music/SET_LAST_VOLUME', 50)
          }
        },
        immediate: true
      }
    },
    mounted() {
      this.checkDevice()
      if (this.$store.getters['music/isPlayByOrder']) {
        this.selectedMusic = -1
      } else {
        this.selectedMusic = this.currentTrack
      }
    },
    methods: {
      setLastVolume(val) {
        this.$store.commit('music/SET_LAST_VOLUME', val)
      },
      closeDialog() {
        this.$nuxt.$emit('root:showMusicPlayerDialogStatus', false)
      },
      setNowVolume() {
        return this.volume === 0 ? (this.volume = this.lastVolume) : (this.volume = 0)
      },
      getVolumeIcon(hover) {
        const isHover = this.touchableDevice ? false : hover
        const isMuted = this.volume === 0
        return isMuted === isHover ? 'volume_up' : 'no_sound'
      },
      checkDevice() {
        this.touchableDevice = 'ontouchend' in document
      }
    }
  }
</script>
<style scoped lang="scss">
  @import '~vuetify/src/styles/settings/_variables.scss';
  $primary-variant-2: map-get($colors, 'primary-variant-2');
  $default-content: map-get($colors, 'default-content');
  $text-slider-hover: map-get($colors, 'text-slider-hover');
  .track-color {
    color: rgba(var(--v-primary-variant-3), 0.4) !important;
  }
  .music-slider-container {
    align-items: center;
    :deep(.v-slider) {
      margin-right: 0 !important;
      cursor: pointer !important;
    }
    :deep(.v-input__prepend-outer) {
      width: 36px !important;
      height: 36px !important;
      margin: 0px 7px 0px -8px !important;
      .v-btn {
        &:hover {
          &::before {
            opacity: 0 !important;
          }
          .v-icon {
            &.needHover {
              color: $primary-variant-2 !important;
              //replaceColor
              color: $text-slider-hover !important;
            }
          }
        }
      }
      .v-icon {
        transition: all 0.4s ease-out;
      }
    }
  }
  .volume-percentage {
    width: 38px;
    text-align: right;
  }
</style>
