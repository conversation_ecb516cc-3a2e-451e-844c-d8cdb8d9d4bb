<template>
  <div :class="{ 'd-none': showNewsArrTmp.length === 0 }">
    <v-row justify="center" no-gutters>
      <v-expansion-panels accordion flat v-model="newsPanels" @change="handlePanelChange">
        <v-expansion-panel v-for="(item, i) in showNewsArrTmp" :key="i" :id="`news${item.id}`">
          <v-expansion-panel-header expand-icon="mdi-menu-down">
            <v-row
              justify="space-between"
              align="center"
              :class="[{ 'pr-6': !$vuetify.breakpoint.xsOnly }, 'default-content--text']"
              no-gutters
            >
              <v-col
                :xl="newsPanels === i ? 12 : 10"
                :lg="newsPanels === i ? 12 : 10"
                :md="newsPanels === i ? 12 : 10"
                sm="12"
                xs="12"
                cols="12"
              >
                <v-row
                  no-gutters
                  :class="[
                    $vuetify.breakpoint.smAndDown ? 'font-weight-medium' : 'font-weight-regular',
                    'default-content--text',
                    'custom-text-noto',
                    'text-xl-subtitle-1',
                    'text-lg-subtitle-1',
                    'text-md-subtitle-1',
                    'text-sm-subtitle-2',
                    'text-subtitle-2',
                    'news-word-break',
                    'title-soft--text'
                  ]"
                  :style="{
                    'line-height':
                      modeTmp === 0
                        ? $vuetify.breakpoint.lgAndUp
                          ? '24px'
                          : '22px'
                        : $vuetify.breakpoint.lgAndUp
                        ? '28px'
                        : '22px'
                  }"
                  >{{ item.title }}
                </v-row>
              </v-col>
              <v-col
                :xl="newsPanels === i ? 12 : 2"
                :lg="newsPanels === i ? 12 : 2"
                :md="newsPanels === i ? 12 : 2"
                sm="12"
                xs="12"
                cols="12"
              >
                <v-row
                  no-gutters
                  :justify="$vuetify.breakpoint.smAndDown || newsPanels === i ? 'start' : 'end'"
                  class="font-weight-regular default-content--text custom-text-noto text-xl-body-2 text-lg-body-2 text-md-body-2 text-sm-caption text-caption word-break-all text-regular--text"
                  :style="{
                    'line-height': '20px'
                  }"
                  >{{ getTimeText(item.releasedAt, newsPanels === i) }}
                </v-row>
              </v-col>
            </v-row>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="px-6">
            <v-row no-gutters>
              <span
                class="font-weight-regular custom-text-noto text-body-2 news-word-break text-regular--text"
                :style="{
                  'line-height': '20px',
                  'text-align': 'start'
                }"
                v-html="item.article"
              />
            </v-row>
          </v-expansion-panel-content>
          <div class="divider mx-6" style="border-bottom: 1px solid; opacity: 0.4" />
        </v-expansion-panel>
      </v-expansion-panels>
    </v-row>
  </div>
</template>

<script>
  import convertTime from '~/utils/convertTime'
  import scroll from '~/mixins/scroll'

  export default {
    name: 'newsComponent',
    mixins: [scroll],
    props: {
      showNewsArr: { type: Array, default: () => [] },
      mode: { type: Number, default: 0 } //0:最新消息 1:公告
    },
    watch: {
      showNewsArr: {
        handler(arr) {
          this.showNewsArrTmp = arr
        },
        deep: true
      },
      mode: {
        handler(val) {
          this.modeTmp = val
        },
        immediate: true
      },
      newsID: {
        handler(newVal, oldVal) {
          if (oldVal !== newVal) {
            this.chooseNews(newVal)
          }
        },
        immediate: true
      }
    },
    data() {
      return {
        newsPanels: null,
        showNewsArrTmp: this.showNewsArr,
        modeTmp: this.mode,
        newsID: null
      }
    },
    created() {},
    methods: {
      closeAllPanels() {
        this.newsPanels = null
      },
      chooseNews(id) {
        this.scrollToID({
          id: id,
          block: 'center',
          behavior: 'smooth',
          timeout: 200
        })
      },
      saveNewsID(id) {
        this.newsID = id
      },
      getTimeText(time, convertDate) {
        const timeStamp = this.$UIConfig.timeStamp
        const timeFormat = convertDate ? timeStamp.formatNewsNote : timeStamp.formatDate
        return convertTime
          .convertISOTime(time, timeFormat, this.$UIConfig.timeStamp.timezone)
          .format(`(${convertTime.getGMTOffset(this.$UIConfig.timeStamp.timezone)}),`)
      },
      async handlePanelChange(panelIndex) {
        //先記錄公告變更前的位置
        const savedPosition = window.scrollY
        const query = { ...this.$route.query }
        if (panelIndex !== null && panelIndex !== undefined) {
          query.newsID = this.showNewsArrTmp[panelIndex].id
        } else {
          delete query.newsID
        }
        // 將改變後的公告參數更新上URL
        await this.$router.replace({ query })
        // 用於維持在原本位置
        this.$nextTick(() => {
          window.scrollTo({
            top: savedPosition,
            behavior: 'instant'
          })
        })
      },
      openNewsFromUrl() {
        const newsIdFromUrl = this.$route.query.newsID
        if (newsIdFromUrl) {
          const index = this.showNewsArrTmp.findIndex(
            (item) => item.id.toString() === newsIdFromUrl
          )
          // 如果有找到對應的公告
          if (index !== -1) {
            this.newsPanels = index
            this.$nextTick(() => {
              this.saveNewsID(`news${newsIdFromUrl}`)
            })
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  $btn-soft: map-get($colors, 'btn-soft');
  .v-expansion-panels::v-deep {
    a[href^='tel'] {
      pointer-events: none;
      color: #ffffff !important;
      text-decoration: none;
    }
    .v-expansion-panel {
      background-color: transparent !important;

      &-content__wrap {
        padding: 0px 0px 16px 0px;
      }
    }
    .news-word-break {
      word-break: break-word;
      overflow-wrap: break-word;
      max-width: 100%;
    }
    .v-expansion-panel-header__icon {
      .v-icon {
        color: $btn-soft !important;
      }
    }
  }
</style>
