<template>
  <v-container v-if="isShow" fluid class="pa-0">
    <!-- live games -->
    <v-row v-if="propGameList.length !== 0" no-gutters>
      <v-col cols="12" :class="{ 'notch-right': hasRightNotch }">
        <v-row no-gutters align="center">
          <span
            class="font-weight-bold text-h5 custom-text-noto mr-2 mr-sm-4 title-heavy--text"
            :class="$UIConfig.homeIndex.titleClass"
          >
            {{ setBlockTitle('live_human') }}
          </span>
          <template v-if="propGameList.length > 6">
            <gradientDivider class="mr-4" />
            <v-btn
              class="my-2"
              outlined
              :color="$UIConfig.replaceColor.btnRegular"
              :small="$vuetify.breakpoint.smAndDown"
              @click="goToLobbyWithGameSortType(propGameSortType, gameCategoryId)"
            >
              {{ setBlockTitle('explore_all') }}
            </v-btn>
          </template>
          <template v-else>
            <gradientDivider />
          </template>
        </v-row>
      </v-col>
      <swiper-custom
        class="mt-4"
        :game-list="propGameList"
        :swiper-slide-style="swiperSlideStyle"
        :show-slide-btn="showSlideBtn"
        :btn-position="btnPosition"
      >
        <template v-slot:card="data">
          <gameCard :game="data.game" :v-card-margin-x="'mx-0'" />
        </template>
      </swiper-custom>
    </v-row>
  </v-container>
</template>
<script>
  import gameRelate from '@/mixins/gameRelate.js'
  import orientation from '@/mixins/orientation.js'

  export default {
    name: 'LiveGameList',
    mixins: [gameRelate, orientation],
    components: {
      gradientDivider: () => import('~/components/gradientDivider.vue'),
      swiperCustom: () => import('~/components/swiperCustom.vue'),
      gameCard: () =>
        import(
          process.env.STATION === 'vietnam_01'
            ? '~/components_station/vietnam_01/game/gameCard.vue'
            : '~/components/game/gameCard.vue'
        )
    },
    props: {
      propGameList: {
        type: Array,
        required: true
      },
      propGameSortType: {
        type: Number,
        required: true
      },
      gameCategoryId: {
        type: Number,
        required: true
      },
      blockTitleCapital: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        // 是否顯示左右按鈕
        showNavigationButtons: true,
        // 左右按鈕的位置
        btnPosition: {
          left: '-15px',
          right: '-15px',
          top: '40%'
        }
      }
    },
    computed: {
      isShow() {
        return this.$UIConfig.lock.specialGame.live
      },
      swiperSlideStyle() {
        const breakWidth = this.$vuetify.breakpoint.width
        const width =
          breakWidth >= 1264
            ? this.$UIConfig.swiperBox.gameCardWidth.lg
            : breakWidth >= 960
            ? this.$UIConfig.swiperBox.gameCardWidth.md
            : breakWidth >= 600
            ? this.$UIConfig.swiperBox.gameCardWidth.sm
            : this.$UIConfig.swiperBox.gameCardWidth.xs
        const body = { boxSizing: 'border-box', width }

        return body
      },
      showSlideBtn() {
        const seriesListLength = this.propGameList.length
        return (
          this.showNavigationButtons &&
          this.$vuetify.breakpoint.lgAndUp &&
          // 卡片張數過少時不顯示左右滑動按鈕
          (this.$vuetify.breakpoint.width <= 1270 ? seriesListLength > 4 : seriesListLength > 6)
        )
      }
    },
    beforeDestroy() {
      this.$store.commit('gameHall/SET_ALL_GAME_LIST', [])
    },
    methods: {
      setBlockTitle(key) {
        return this.blockTitleCapital ? this.$t(key).toUpperCase() : this.$t(key)
      }
    }
  }
</script>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
