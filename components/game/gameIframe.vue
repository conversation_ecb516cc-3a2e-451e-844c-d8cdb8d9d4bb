<template>
  <div>
    <v-dialog
      fullscreen
      persistent
      id="gameDialog"
      content-class="noscroll"
      transition="dialog-bottom-transition"
      v-model="showGameIframeStatusTmp"
    >
      <v-card
        class="d-flex"
        :class="{
          'flex-column': !showDrawer,
          'flex-column-reverse': !showDrawer && !isPCOrTablet,
          'flex-row': showDrawer
        }"
      >
        <v-navigation-drawer
          v-model="showDrawer"
          mini-variant
          :mini-variant-width="isIosAndHorizontal ? 84 : 56"
          color="iframe-bar"
          fixed
          :permanent="
            deviceWidth > deviceHeight && $device.isMobile && ($device.isIos || $device.isAndroid)
          "
          :class="{ 'pl-7': isIosAndHorizontal }"
        >
          <div class="d-flex flex-wrap justify-center">
            <v-btn
              class="pa-0 mx-1 mt-1"
              color="transparent"
              min-width="44px"
              min-height="44px"
              @click="closeAction"
            >
              <v-icon color="default-content"> mdi-chevron-left </v-icon>
            </v-btn>
            <!-- chat -->
            <v-btn
              v-if="isLogin"
              class="pa-0 mx-1"
              color="transparent"
              min-width="44px"
              min-height="44px"
              @click="goChat()"
            >
              <v-icon color="default-content"> mdi-message-processing-outline </v-icon>
            </v-btn>
            <!-- customer service -->
            <v-btn
              class="pa-0 mx-1"
              color="transparent"
              min-width="44px"
              min-height="44px"
              @click="goCustomerService()"
            >
              <v-icon color="default-content">mdi-face-agent</v-icon>
            </v-btn>
            <v-tooltip right v-if="xinkey">
              <template v-slot:activator="{ on, attrs }">
                <v-card
                  class="d-flex align-center"
                  height="44px"
                  width="44px"
                  color="transparent"
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-img
                    :src="getImage('<EMAIL>')"
                    :srcset="getSrcset('coin')"
                    min-width="24"
                    max-width="24"
                    height="24"
                    class="ma-2 d-inline-block vertical-middle"
                  />
                </v-card>
              </template>
              <span> {{ $t('formal_play') }}</span>
            </v-tooltip>
          </div>
        </v-navigation-drawer>
        <v-toolbar
          class="pa-0 mx-0"
          v-show="appBarStatus"
          dark
          dense
          color="iframe-bar"
          :height="
            $device.isMobile
              ? $device.isAndroid
                ? '56px'
                : $device.isIos
                ? '70px'
                : '50px'
              : '50px'
          "
        >
          <v-row v-if="!$vuetify.breakpoint.xsOnly" class="px-3 mx-0" no-gutters align="center">
            <v-col cols="4">
              <v-row no-gutters class="d-flex flex-nowrap">
                <v-btn
                  class="pa-2 mr-1"
                  color="transparent"
                  min-width="64px"
                  min-height="36px"
                  @click="closeAction"
                >
                  <v-icon color="default-content"> mdi-chevron-left </v-icon>
                  <span v-if="!$vuetify.breakpoint.xsOnly" class="ml-2">
                    {{ $t('back_lobby') }}
                  </span>
                </v-btn>
                <!-- chat -->
                <v-btn
                  v-if="isLogin"
                  class="pa-2 mr-1"
                  color="transparent"
                  min-width="64px"
                  min-height="36px"
                  @click="goChat()"
                >
                  <v-icon color="default-content"> mdi-message-processing-outline </v-icon>
                  <span v-if="!$vuetify.breakpoint.xsOnly" class="default-content--text ml-2">
                    {{ $t('chat_session') }}
                  </span>
                </v-btn>
                <!-- customer service -->
                <v-btn
                  class="pa-2 mr-1"
                  color="transparent"
                  min-width="64px"
                  min-height="36px"
                  @click="goCustomerService()"
                >
                  <v-icon color="default-content">mdi-face-agent</v-icon>
                  <span v-if="!$vuetify.breakpoint.xsOnly" class="default-content--text ml-2">
                    {{ $t('customer_service') }}
                  </span>
                </v-btn>
              </v-row>
            </v-col>
            <v-col cols="4">
              <v-row no-gutters justify="center">
                <v-toolbar-title>
                  <v-img
                    :src="logoImg"
                    contain
                    :height="$UIConfig.gameIframe.logoHeight"
                    @error="imgError()"
                  />
                </v-toolbar-title>
              </v-row>
            </v-col>
            <v-col cols="4" class="d-flex justify-end align-center">
              <video
                v-if="env !== 'production' && $device.isWindows"
                ref="videoElement"
                autoplay
                playsInline
                muted
                class="video-element"
              ></video>
              <v-btn
                v-if="env !== 'production' && $device.isWindows"
                class="default-content pa-0 mr-1"
                color="transparent"
                min-width="44px"
                min-height="44px"
                @click="captureAndDownload"
              >
                <v-icon color="default-content"> mdi-camera </v-icon>
              </v-btn>
              <v-tooltip bottom v-if="xinkey">
                <template v-slot:activator="{ on, attrs }">
                  <v-card
                    class="d-flex align-center"
                    height="44px"
                    width="44px"
                    color="transparent"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-img
                      :src="getImage('<EMAIL>')"
                      :srcset="getSrcset('coin')"
                      min-width="24"
                      max-width="24"
                      height="24"
                      class="ma-2 d-inline-block vertical-middle"
                    />
                  </v-card>
                </template>
                <span> {{ $t('formal_play') }}</span>
              </v-tooltip>
              <v-btn
                v-if="$vuetify.breakpoint.lgAndUp && !$device.isTablet"
                class="default-content pa-0 ma-0 mr-1"
                color="transparent"
                min-width="44px"
                min-height="44px"
                @click="toggleFullscreen"
              >
                <v-icon>mdi-fullscreen</v-icon>
              </v-btn>
            </v-col>
          </v-row>
          <v-row v-else class="px-0 mx-0" no-gutters align="center">
            <v-col class="pa-0 ma-0 d-flex flex-nowrap align-center">
              <v-btn
                class="pa-0"
                color="transparent"
                min-width="44px"
                min-height="44px"
                @click="closeAction"
              >
                <v-icon color="default-content"> mdi-chevron-left </v-icon>
              </v-btn>
              <v-img
                :src="logoImg"
                contain
                :height="$UIConfig.gameIframe.logoHeight"
                max-width="125"
                @error="imgError()"
              />
              <v-tooltip top v-if="xinkey">
                <template v-slot:activator="{ on, attrs }">
                  <v-card
                    class="d-flex align-center"
                    height="44px"
                    width="44px"
                    color="transparent"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-img
                      :src="getImage('<EMAIL>')"
                      :srcset="getSrcset('coin')"
                      min-width="24"
                      max-width="24"
                      height="24"
                      class="ma-2 d-inline-block vertical-middle"
                    />
                  </v-card>
                </template>
                <span> {{ $t('formal_play') }}</span>
              </v-tooltip>
            </v-col>
            <v-col class="d-flex justify-end">
              <!-- chat -->
              <v-btn
                v-if="isLogin"
                class="pa-0"
                color="transparent"
                min-width="44px"
                min-height="44px"
                @click="goChat()"
              >
                <v-icon color="default-content"> mdi-message-processing-outline </v-icon>
              </v-btn>
              <!-- customer service -->
              <v-btn
                class="pa-0"
                color="transparent"
                min-width="44px"
                min-height="44px"
                @click="goCustomerService()"
              >
                <v-icon color="default-content">mdi-face-agent</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-toolbar>
        <div
          :class="{
            'iframe-height-horizon': showDrawer,
            'iframe-height-vertical': !showDrawer && !$device.isMobile,
            'iframe-height-vertical-android': !showDrawer && $device.isMobile && $device.isAndroid,
            'iframe-height-vertical-ios': !showDrawer && $device.isMobile && $device.isIos
          }"
          :style="{ 'margin-left': showDrawer ? (isIosAndHorizontal ? '84px' : '56px') : '0' }"
        >
          <iframe
            v-if="isNonSandboxGame"
            :src="gameLink"
            scrolling="no"
            id="gameIframe"
            style="border: 0"
          >
          </iframe>
          <iframe
            v-else
            :src="gameLink"
            scrolling="no"
            id="gameIframe"
            sandbox="allow-scripts allow-same-origin allow-orientation-lock"
            style="border: 0"
          >
          </iframe>
        </div>
      </v-card>
    </v-dialog>
    <confirmLeaveGame
      v-if="showConfirmBackGameDialog"
      :show-confirm-back-game-dialog.sync="showConfirmBackGameDialog"
      @backToParent="backToParent"
    ></confirmLeaveGame>
  </div>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import orientation from '~/mixins/orientation'
  import images from '~/mixins/images'
  const STATION = process.env.STATION
  export default {
    name: 'gameIframe',
    mixins: [hiddenScrollHtml, orientation, images],
    components: {
      confirmLeaveGame: () => import('~/components/game/confirmLeaveGameDialog.vue')
    },
    props: {
      showGameIframeStatus: {
        type: Boolean,
        required: true,
        default: false
      }
    },
    data() {
      return {
        showDrawer: false,
        showGameIframeStatusTmp: this.showGameIframeStatus,
        appBarStatus: false,
        env: process.env.NUXT_ENV,
        showFullScreenStatus: false,
        showConfirmBackGameDialog: false,
        showNotification: false,
        deviceWidth: 0,
        deviceHeight: 0,
        logoImg: `/${STATION}/logo.webp`
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      gameLink({ $store }) {
        return $store.getters['gameHall/gameLink']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      xinkey({ $store }) {
        return $store.getters['gameHall/xinkey']
      },
      isPlaying({ $store }) {
        return $store.getters['music/isPlaying']
      },
      singleGameHallInfo({ $store }) {
        return $store.getters['gameHall/singleGameHallInfo']
      },

      isPCOrTablet() {
        return (
          this.$device.isDesktopOrTablet &&
          (this.$device.isMacOS || this.$device.isSamsung || this.$device.isWindows)
        )
      },
      isIosAndHorizontal() {
        return this.$device.isIos && (this.orientation === 90 || this.orientation === 270)
      },
      isNonSandboxGame() {
        return Boolean(this.singleGameHallInfo.canRedirect)
      },
      station({ $store }) {
        return $store.getters['station']
      },
      isDisconnect({ $store, $xinConfig }) {
        return $store.getters['xinProtocol/services'][$xinConfig.WEB_GAME_SERVICE.ID].connected
      }
    },
    watch: {
      showGameIframeStatus: {
        async handler(status) {
          setTimeout(() => {
            this.$store.commit('gameHall/SET_OPENGAMELOCK', false)
          }, 2000)
          if (this.maintainSystem[0].maintaining) {
            return
          }
          if (this.isPlaying) {
            this.$store.commit('music/SET_IS_PLAYING', false)
          }

          if (status) {
            document.documentElement.classList.add('overflow-y-hidden')
          } else {
            document.documentElement.classList.remove('overflow-y-hidden')
          }
          this.showGameIframeStatusTmp = status
        },
        immediate: true
      },
      isDisconnect: {
        handler(val) {
          if (!val) {
            // 確定 LOGOUT，需要重置 id
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: this.$xinConfig.WEB_GAME_SERVICE.ID,
              enable: false,
              connected: false,
              sendId: 0,
              receiveId: 0
            })
            this.$notify.error(this.$t('game_disconnect_reenter'))
          }
        }
      }
    },
    created() {
      if (this.$device.isIos || this.$device.isMacOS) {
        this.logoImg = `/${STATION}/logo.png`
      }
      this.$store.commit('menu/SET_SHOWGAMEMODESTATUS', true)
    },
    mounted() {
      this.renderResize()
      window.addEventListener('resize', this.renderResize, false)
      //倒數30秒
      let seconds = 3
      const countdown = setInterval(() => {
        seconds--
        if (seconds === 0) {
          this.showNotification = true
          clearInterval(countdown)
        }
      }, 1000)
    },
    destroyed() {
      window.removeEventListener('resize', this.renderResize, false)
      this.$store.commit('menu/SET_SHOWGAMEMODESTATUS', false)
    },
    methods: {
      async getDisplayMedia(options) {
        if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
          return await navigator.mediaDevices.getDisplayMedia(options)
        }
        if (navigator.getDisplayMedia) {
          return await navigator.getDisplayMedia(options)
        }
        if (navigator.webkitGetDisplayMedia) {
          return await navigator.webkitGetDisplayMedia(options)
        }
        if (navigator.mozGetDisplayMedia) {
          return await navigator.mozGetDisplayMedia(options)
        }
        throw new Error('getDisplayMedia is not defined')
      },
      stopCapture() {
        if (!this.$refs.videoElement.srcObject) {
          return
        }
        let tracks = this.$refs.videoElement.srcObject.getTracks()
        tracks.forEach((track) => track.stop())
        this.$refs.videoElement.srcObject = null
      },
      async startCapture() {
        try {
          const mode =
            this.singleGameHallInfo.xinkey === undefined ? this.$t('freePlay') : this.$t('play')
          const title = '截圖資訊'
          const showString =
            '截圖時間點: ' +
            this.$moment().format() +
            '<br>模式: ' +
            mode +
            '<br>玩家名稱: ' +
            this.userName +
            '<br>遊戲平台編號: ' +
            this.singleGameHallInfo.gameId +
            '<br>遊戲名稱: ' +
            this.singleGameHallInfo.gameName +
            '<br>xinkey: ' +
            this.singleGameHallInfo.xinkey +
            '<br>玩家IP: ' +
            this.singleGameHallInfo.ip +
            '<br>userAgent: ' +
            this.singleGameHallInfo.userAgent
          this.$store.commit('easyDialog/SET_COPYSTATUS', true)
          this.$store.dispatch('easyDialog/setDialog', {
            title: title,
            message: showString
          })

          let width = screen.width * (window.devicePixelRatio || 1)
          let height = screen.height * (window.devicePixelRatio || 1)
          console.log(width, height)
          const displayMediaOptions = {
            // 参数的详情可以在mdn网站查找
            audio: false,
            video: {
              width,
              height,
              frameRate: 1
            },
            preferCurrentTab: true
          }

          if (this.$device.isMobile) {
            displayMediaOptions.video.mediaSource = 'screen'
          }

          await this.getDisplayMedia(displayMediaOptions)
            .then(async (stream) => {
              this.$refs.videoElement.srcObject = stream
              await this.$refs.videoElement.play()
              await this.$refs.videoElement.pause()
            })
            .catch((error) => {
              width = 0
              height = 0
              console.log(error)
            })
          return { width, height }
        } catch (error) {
          console.error('Error accessing screen capture:', error)
        }
      },
      captureFrame({ width, height }) {
        const canvas = document.createElement('canvas')
        canvas.width = width
        canvas.height = height

        canvas
          .getContext('2d')
          .drawImage(this.$refs.videoElement, 0, 0, canvas.width, canvas.height)
        return canvas
      },
      downloadAsPNG(canvas) {
        let now = this.$moment().format()
        const dataURL = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.href = dataURL
        link.download =
          this.singleGameHallInfo.gameId +
          '_' +
          this.singleGameHallInfo.gameName +
          '_' +
          now +
          '.png'
        link.click()
        this.stopCapture()
      },
      async captureAndDownload() {
        const size = await this.startCapture()
        if (size.width === 0 && size.height === 0) {
          return
        }
        const canvas = this.captureFrame({ width: size.width, height: size.height })
        this.downloadAsPNG(canvas)
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      toggleFullscreen() {
        if (!this.$screenfull.isEnabled) {
          return
        }
        this.showFullScreenStatus = !this.showFullScreenStatus
        if (this.showFullScreenStatus) {
          this.$screenfull.request()
        } else {
          this.$screenfull.exit()
        }
      },
      renderResize() {
        // 判断横竖屏
        this.setVH()
        this.deviceWidth = Number(document.documentElement.clientWidth)
        this.deviceHeight = Number(document.documentElement.clientHeight)
        let isHorizontal = this.deviceWidth > this.deviceHeight
        if (!isHorizontal && this.$device.isMobile && this.$device.isIos) {
          //ios-直
          this.appBarStatus = true
          this.showDrawer = false
        } else if (!isHorizontal && this.$device.isMobile && this.$device.isAndroid) {
          //android-直
          this.appBarStatus = true
          this.showDrawer = false
        } else if (isHorizontal && this.$device.isMobile && this.$device.isIos) {
          //ios-橫
          this.appBarStatus = false
          this.showDrawer = true
        } else if (isHorizontal && this.$device.isMobile && this.$device.isAndroid) {
          //android-橫
          this.appBarStatus = false
          this.showDrawer = true
        } else if (isHorizontal && this.$device.isTablet) {
          //一般平板橫
          this.appBarStatus = true
          this.showDrawer = false
        } else if (
          isHorizontal &&
          ((this.$device.isDesktopOrTablet &&
            this.$device.isMacOS &&
            this.$vuetify.breakpoint.mdAndDown) ||
            (this.$device.isDesktopOrTablet &&
              this.$device.isSamsung &&
              this.$vuetify.breakpoint.mdAndDown))
        ) {
          //Samsung & MacOS 平板橫
          this.appBarStatus = true
          this.showDrawer = false
        } else if (this.$vuetify.breakpoint.smAndUp && this.$device.isDesktopOrTablet) {
          // PC
          this.appBarStatus = true
          this.showDrawer = false
        }
      },
      backToParent() {
        if (!this.isPlaying) {
          this.$store.commit('music/SET_IS_PLAYING', true)
        }

        // 回到父頁面
        this.$nuxt.$emit('root:showGameIframeStatus', false)
        this.$store.commit('gameHall/SET_GAME_LINK', '')
        setTimeout(() => {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())
        }, 2000)
      },
      setVH() {
        var vh = window.innerHeight * 0.01
        document.documentElement.style.setProperty('--vh', `${vh}px`)
      },
      // 按下返回鍵觸發
      closeAction() {
        if (this.showNotification && this.xinkey) {
          this.showConfirmBackGameDialog = true
        } else {
          this.backToParent()
        }
      },
      goChat() {
        this.$nuxt.$emit('root:showChatRoomDialogStatus', true)
      },
      async goCustomerService() {
        if (this.maintainSystem[0].maintaining) return
        this.$nuxt.$emit('root:showCustomerServiceDialogStatusEvent', true)
      },
      async imgError() {
        this.logoImg = `/${STATION}/logo.png`
      }
    }
  }
</script>
<style lang="scss" scoped>
  .iframe-height-horizon {
    width: calc(100vw - 56px);
    height: calc(var(--vh, 1vh) * 100);
  }
  .iframe-height-vertical {
    height: calc(var(--vh, 1vh) * 100 - 50px);
  }
  .iframe-height-vertical-android {
    height: calc(var(--vh, 1vh) * 100 - 56px);
  }
  .iframe-height-vertical-ios {
    height: calc(var(--vh, 1vh) * 100 - 70px);
  }
  .v-toolbar ::v-deep {
    .v-toolbar__content {
      padding: 4px 4px !important;
    }
  }

  .video-element {
    display: none;
  }
  html,
  body {
    width: 100vw;
  }
  iframe {
    width: 100%;
    height: 100%;
  }
</style>
