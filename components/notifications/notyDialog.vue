<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog v-model="showNotyDialogStatusTmp" persistent max-width="380" content-class="rounded-lg">
    <v-card class="dialog-fill pa-4 pa-sm-6">
      <v-card-title
        class="custom-text-noto text-h6 grey-1--text justify-center pa-0"
        v-html="title"
      />
      <v-card-text
        class="custom-text-noto text-body-2 default-content--text word-break px-0 py-6"
        v-html="message"
      />
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2"
            ><v-btn
              v-if="isLogin && copyStatus"
              class="default-content w-100"
              :small="breakpoint.xsOnly"
              color="transparent"
              elevation="0"
              v-clipboard:copy="message.replace(/<br>/g, '\r\n')"
              v-clipboard:success="onCopy"
              v-clipboard:error="onError"
            >
              <v-icon>mdi-camera</v-icon>
            </v-btn></v-col
          >
          <v-col
            :cols="
              !(isLogin && copyStatus) && breakpoint.xsOnly
                ? '12'
                : breakpoint.xsOnly
                ? '6'
                : 'auto'
            "
            :class="!(isLogin && copyStatus) && breakpoint.xsOnly ? '' : 'pl-2'"
          >
            <v-btn
              depressed
              :color="$UIConfig.defaultBtnColor"
              class="button-content--text w-100"
              @click="closeDialog"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import converter from '~/mixins/converter'

  export default {
    name: 'NotyDialog',
    mixins: [hiddenScrollHtml, converter],
    props: {
      showNotyDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showNotyDialogStatusTmp: this.showNotyDialogStatus
      }
    },
    watch: {
      showNotyDialogStatus: {
        handler(status) {
          this.showNotyDialogStatusTmp = status
          if (!status) this.$store.commit('easyDialog/RESET')
        },
        immediate: true
      }
    },
    mounted() {},
    beforeDestroy() {
      this.$store.commit('easyDialog/RESET')
    },
    methods: {
      onCopy: function () {
        this.$notify.success('success to copy texts')
      },
      onError: function () {
        this.$notify.error('Failed to copy texts')
      },
      closeDialog() {
        this.$nuxt.$emit('root:showNotyDialogStatus', false)
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      title({ $store }) {
        return this.convertMessage($store.getters['easyDialog/GET_TITLE']).toUpperCase()
      },
      message({ $store }) {
        return this.convertMessage($store.getters['easyDialog/GET_MESSAGE'])
      },
      copyStatus({ $store }) {
        return $store.getters['easyDialog/GET_COPYSTATUS']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    }
  }
</script>
<style lang="scss" scoped>
  .word-break {
    white-space: pre-wrap;
  }
</style>
