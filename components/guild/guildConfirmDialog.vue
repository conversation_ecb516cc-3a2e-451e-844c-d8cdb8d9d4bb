<template>
  <v-dialog
    v-model="showGuildConfirmDialogStatusTmp"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card color="transparent" class="pa-4 pa-sm-6">
      <v-card-title class="custom-text-noto text-h6 grey-1--text justify-center pa-0">
        {{ $t('reminder').toUpperCase() }}
      </v-card-title>
      <v-card-text
        class="default-content--text text-body-2 custom-text-noto px-0 py-6"
        v-html="confirmObj.confirmInfo"
      >
      </v-card-text>
      <v-card-actions class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
            <v-btn
              :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="cancelDialog"
            >
              {{ $t(confirmObj.cancel).toUpperCase() }}
            </v-btn>
          </v-col>
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              :color="$UIConfig.defaultBtnColor"
              depressed
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              @click="onConfirm"
            >
              {{ $t(confirmObj.confirm).toUpperCase() }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'guildConfirmDialog',
    props: {
      showGuildConfirmDialogStatus: {
        type: Boolean,
        required: true
      },
      confirmObj: {
        type: Object,
        required: true,
        default: () => ({
          confirm: '',
          confirmInfo: '',
          cancel: '',
          onConfirmNotify: () => {}
        })
      },
      hasGuildLetter: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showGuildConfirmDialogStatusTmp: false
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showGuildConfirmDialogStatus: {
        immediate: true,
        handler(val) {
          this.showGuildConfirmDialogStatusTmp = val
        }
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showGuildConfirmDialogStatus', false)
      },
      onConfirm() {
        this.closeDialog()
        if (this.hasGuildLetter) {
          this.$notify.warning(this.$t('guild_invite_exist_noty'))
        } else {
          this.confirmObj.onConfirmNotify()
        }
      },
      cancelDialog() {
        this.closeDialog()
      }
    }
  }
</script>

<style></style>
