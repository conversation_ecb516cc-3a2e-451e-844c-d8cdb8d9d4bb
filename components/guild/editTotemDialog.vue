<template>
  <v-dialog
    v-model="showEditTotemDialogStatusTmp"
    draggable="true"
    width="498"
    persistent
    :fullscreen="breakpoint.xsOnly"
    :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
  >
    <v-card color="transparent">
      <customDialogTitle :title="$t('edit_photo').toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text class="pa-4 pa-sm-6">
        <div class="img-content d-flex justify-center">
          <vueAdvancedCropper
            :src="option.img"
            ref="cropper"
            class="cropper-content"
            background-class="grey-6"
            :resize-image="{
              touch: option.canScale,
              adjustStencil: option.canScale
            }"
            :move-image="{ touch: option.canMove, mouse: option.canMove }"
            image-restriction="fit-area"
            :stencil-props="{
              aspectRatio: option.aspectRatio,
              movable: true,
              resizable: true
            }"
            :canvas="{
              minHeight: 0,
              minWidth: 0,
              maxHeight: option.autoCropHeight,
              maxWidth: option.autoCropWidth
            }"
            :auto-zoom="option.canScale"
            @ready="imgLoad"
          ></vueAdvancedCropper>
          <v-overlay :absolute="absolute" :value="overlay">
            <v-progress-circular indeterminate size="64"></v-progress-circular>
          </v-overlay>
        </div>
      </v-card-text>
      <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
        <!-- 站台差異註解 -->
        <v-btn
          :disabled="loadTimer"
          class="button-content--text w-100"
          :color="$UIConfig.defaultBtnColor"
          depressed
          @click="prepareUpdateAvatar"
        >
          <span class="text-buttom custom-text-noto">
            {{ $t('upload').toUpperCase() }}
          </span>
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import uploadPhoto from '@/mixins/uploadPhoto.js'
  import guildMgr from '~/mixins/guildMgr'

  import 'vue-advanced-cropper/dist/style.css'
  import 'vue-advanced-cropper/dist/theme.bubble.css'
  export default {
    name: 'editTotemDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    mixins: [uploadPhoto, hiddenScrollHtml, guildMgr],
    props: {
      showEditTotemDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showEditTotemDialogStatusTmp: this.showEditTotemDialogStatus,
        image: null,
        //圖片剪裁設定
        option: {
          img: '', // 圖片來源
          outputType: 'png', // 產生圖片的格式
          autoCrop: true, // 是否要有截圖框
          autoCropWidth: 200, // 截圖框寬
          autoCropHeight: 200, //截圖框高
          aspectRatio: 1 / 1, //截圖框比例
          canMove: false, //是否可移動圖片
          canScale: false, //是否可拉伸圖片
          fixed: true, //鎖定截圖框比例
          centerBox: true, //截圖框鎖定於圖片內
          maxImgSize: 450,
          mode: 'contain',
          high: true
        },
        absolute: true,
        opacity: 1,
        overlay: true,
        loadTimer: null // 新增計時器
      }
    },
    mounted() {
      // 啟動計時器
      this.startLoadTimer()

      if (this.$liff.isInClient() && this.$device.isIos) {
        const reader = new FileReader()
        reader.onload = (event) => {
          const imageUrl = event.target.result
          this.option.img = imageUrl
        }
        reader.readAsDataURL(this.selectPhoto)
      } else if (this.$device.isIos) {
        this.processImage(this.selectPhoto).then((processedImage) => {
          this.option.img = processedImage
        })
      } else {
        const img = URL.createObjectURL(this.selectPhoto)
        this.option.img = img
      }
    },
    watch: {
      showEditTotemDialogStatus: {
        handler(val) {
          this.showEditTotemDialogStatus = val
        }
      }
    },
    computed: {
      selectPhoto({ $store }) {
        return $store.getters['role/selectPhoto']
      },
      isH5({ $store }) {
        return $store.getters['isH5']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      closeDialog() {
        this.clearLoadTimer()
        this.$emit('update:showEditTotemDialogStatus', false)
      },
      async prepareUpdateAvatar() {
        this.$nuxt.$loading.start()
        const { canvas } = this.$refs.cropper.getResult()
        const canvasImage = canvas.toDataURL()
        this.guildTotemTemp = canvasImage
        const updateAvatar = this.$store.getters['guild/updateGuildAvatar']
        this.$store.commit('guild/SET_UPDATE_GUILD_AVATAR', !updateAvatar)
        this.$nuxt.$loading.finish()
        this.closeDialog()
      },
      imgLoad() {
        this.clearLoadTimer()
        this.overlay = false
      },
      async processImage(file) {
        return new Promise((resolve) => {
          const img = new Image()

          img.onload = () => {
            // 建立 canvas 並調整大小
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')

            // 計算新尺寸，限制最大尺寸為 1920x1920
            const MAX_SIZE = 1920
            let width = img.width
            let height = img.height

            if (width > MAX_SIZE || height > MAX_SIZE) {
              const ratio = Math.min(MAX_SIZE / width, MAX_SIZE / height)
              width *= ratio
              height *= ratio
            }

            // 設定 canvas 尺寸
            canvas.width = width
            canvas.height = height

            // 繪製圖片
            ctx.drawImage(img, 0, 0, width, height)

            // 轉換為 JPEG，使用 0.92 的品質
            const jpegDataUrl = canvas.toDataURL('image/jpeg', 0.92)
            resolve(jpegDataUrl)
          }

          // 讀取原始圖片
          const reader = new FileReader()
          reader.onload = (e) => {
            img.src = e.target.result
          }
          reader.readAsDataURL(file)
        })
      },
      // 啟動計時器
      startLoadTimer() {
        this.loadTimer = setTimeout(() => {
          // 10秒後如果計時器還存在，說明圖片加載超時
          if (this.loadTimer) {
            this.$notify.error(this.$t('load_timeout'))
            this.closeDialog()
          }
        }, 10000)
      },
      // 清除計時器
      clearLoadTimer() {
        if (this.loadTimer) {
          clearTimeout(this.loadTimer)
          this.loadTimer = null
        }
      }
    },
    beforeDestroy() {
      this.clearLoadTimer()
      this.$store.commit('role/SET_SELECT_PHOTO', null)
    }
  }
</script>

<style scoped>
  .vue-cropper {
    background-image: none !important;
  }
  .img-content {
    overflow: hidden;
    position: relative;
    height: 300px;
  }
  .cropper-content {
    width: 100%;
    height: 300px;
  }
</style>
