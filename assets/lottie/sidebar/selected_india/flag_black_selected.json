{"v": "5.9.0", "fr": 24, "ip": 0, "op": 48, "w": 300, "h": 300, "nm": "flag_black_selected", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "圖層 5/icon Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [149.75, 241.35, 0], "e": [149.75, 227.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [149.75, 227.35, 0], "e": [149.75, 241.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [149.75, 241.35, 0], "e": [149.75, 235.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [149.75, 235.35, 0], "e": [149.75, 241.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 9}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [81.25, 183.85, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100], "e": [100, 104.263, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 3, "s": [100, 104.263, 100], "e": [100, 89.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 5, "s": [100, 89.615, 100], "e": [100, 105.17, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 7, "s": [100, 105.17, 100], "e": [100, 100, 100]}, {"t": 9}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -95.863], [-14.75, -95.8], [-33.25, -95.8], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 12.2], [-16.75, 12.2], [7.955, 12.2], [12.275, 26.3], [23.75, 26.3], [40.25, 26.3], [57.75, 26.3], [75.896, 26.3], [81, 21.196], [81, -82.346], [75.896, -87.45], [58.75, -87.45], [45.25, -87.45], [37.813, -87.512], [27.583, -87.512], [24.33, -90.527]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -95.863], [-14.75, -95.8], [-33.25, -95.8], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 12.2], [-16.75, 12.2], [7.955, 12.2], [12.275, 26.3], [23.75, 26.3], [40.25, 26.3], [57.75, 26.3], [75.896, 26.3], [81, 21.196], [81, -82.346], [75.896, -87.45], [58.75, -87.45], [45.25, -87.45], [37.813, -87.512], [27.583, -87.512], [24.33, -90.527]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[0.759, -101.55], [-14.75, -101.55], [-33.25, -99.3], [-48.75, -96.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 11.2], [-33.25, 8.7], [-16.75, 6.45], [-3.92, 6.45], [5.4, 7.175], [23.75, 6.425], [43, 3.425], [60.5, 0.925], [78.646, 0.925], [83.75, -4.179], [83.75, -102.346], [78.646, -107.45], [61.5, -107.45], [48, -104.95], [30.75, -102.45], [20.52, -101.2], [14.267, -101.465]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[0.759, -101.55], [-14.75, -101.55], [-33.25, -99.3], [-48.75, -96.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 11.2], [-33.25, 8.7], [-16.75, 6.45], [-3.92, 6.45], [5.4, 7.175], [23.75, 6.425], [43, 3.425], [60.5, 0.925], [78.646, 0.925], [83.75, -4.179], [83.75, -102.346], [78.646, -107.45], [61.5, -107.45], [48, -104.95], [30.75, -102.45], [20.52, -101.2], [14.267, -101.465]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[1.009, -77.05], [-14.5, -78.3], [-33.25, -81.8], [-48.75, -86.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 21.2], [-37.75, 25.45], [-21.5, 29.2], [-7.67, 31.45], [6.65, 31.175], [23, 30.925], [44, 27.925], [62.5, 23.925], [78.646, 18.425], [83.75, 13.321], [83.75, -84.846], [78.646, -89.95], [63, -86.7], [50.25, -82.7], [34.25, -79.45], [22.52, -77.7], [13.517, -77.215]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[1.009, -77.05], [-14.5, -78.3], [-33.25, -81.8], [-48.75, -86.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 21.2], [-37.75, 25.45], [-21.5, 29.2], [-7.67, 31.45], [6.65, 31.175], [23, 30.925], [44, 27.925], [62.5, 23.925], [78.646, 18.425], [83.75, 13.321], [83.75, -84.846], [78.646, -89.95], [63, -86.7], [50.25, -82.7], [34.25, -79.45], [22.52, -77.7], [13.517, -77.215]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -99.613], [-14.75, -97.8], [-33.25, -96.3], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 11.7], [-16.75, 10.2], [7.955, 8.45], [12.275, 22.55], [23.75, 22.55], [40.25, 23.05], [57.75, 23.8], [75.896, 23.8], [81, 18.696], [81, -84.846], [75.896, -89.95], [58.75, -89.95], [45.25, -90.7], [37.813, -90.762], [27.583, -91.262], [24.33, -94.277]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -99.613], [-14.75, -97.8], [-33.25, -96.3], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 11.7], [-16.75, 10.2], [7.955, 8.45], [12.275, 22.55], [23.75, 22.55], [40.25, 23.05], [57.75, 23.8], [75.896, 23.8], [81, 18.696], [81, -84.846], [75.896, -89.95], [58.75, -89.95], [45.25, -90.7], [37.813, -90.762], [27.583, -91.262], [24.33, -94.277]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -95.863], [-14.75, -95.8], [-33.25, -95.8], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 12.2], [-16.75, 12.2], [7.955, 12.2], [12.275, 26.3], [23.75, 26.3], [40.25, 26.3], [57.75, 26.3], [75.896, 26.3], [81, 21.196], [81, -82.346], [75.896, -87.45], [58.75, -87.45], [45.25, -87.45], [37.813, -87.512], [27.583, -87.512], [24.33, -90.527]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -95.863], [-14.75, -95.8], [-33.25, -95.8], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 12.2], [-16.75, 12.2], [7.955, 12.2], [12.275, 26.3], [23.75, 26.3], [40.25, 26.3], [57.75, 26.3], [75.896, 26.3], [81, 21.196], [81, -82.346], [75.896, -87.45], [58.75, -87.45], [45.25, -87.45], [37.813, -87.512], [27.583, -87.512], [24.33, -90.527]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[0.759, -101.55], [-14.75, -101.55], [-33.25, -99.3], [-48.75, -96.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 11.2], [-33.25, 8.7], [-16.75, 6.45], [-3.92, 6.45], [5.4, 7.175], [23.75, 6.425], [43, 3.425], [60.5, 0.925], [78.646, 0.925], [83.75, -4.179], [83.75, -102.346], [78.646, -107.45], [61.5, -107.45], [48, -104.95], [30.75, -102.45], [20.52, -101.2], [14.267, -101.465]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[0.759, -101.55], [-14.75, -101.55], [-33.25, -99.3], [-48.75, -96.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 11.2], [-33.25, 8.7], [-16.75, 6.45], [-3.92, 6.45], [5.4, 7.175], [23.75, 6.425], [43, 3.425], [60.5, 0.925], [78.646, 0.925], [83.75, -4.179], [83.75, -102.346], [78.646, -107.45], [61.5, -107.45], [48, -104.95], [30.75, -102.45], [20.52, -101.2], [14.267, -101.465]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[1.009, -77.05], [-14.5, -78.3], [-33.25, -81.8], [-48.75, -86.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 21.2], [-37.75, 25.45], [-21.5, 29.2], [-7.67, 31.45], [6.65, 31.175], [23, 30.925], [44, 27.925], [62.5, 23.925], [78.646, 18.425], [83.75, 13.321], [83.75, -84.846], [78.646, -89.95], [63, -86.7], [50.25, -82.7], [34.25, -79.45], [22.52, -77.7], [13.517, -77.215]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-5.017, -0.335]], "v": [[1.009, -77.05], [-14.5, -78.3], [-33.25, -81.8], [-48.75, -86.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 21.2], [-37.75, 25.45], [-21.5, 29.2], [-7.67, 31.45], [6.65, 31.175], [23, 30.925], [44, 27.925], [62.5, 23.925], [78.646, 18.425], [83.75, 13.321], [83.75, -84.846], [78.646, -89.95], [63, -86.7], [50.25, -82.7], [34.25, -79.45], [22.52, -77.7], [13.517, -77.215]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -99.613], [-14.75, -97.8], [-33.25, -96.3], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 11.7], [-16.75, 10.2], [7.955, 8.45], [12.275, 22.55], [23.75, 22.55], [40.25, 23.05], [57.75, 23.8], [75.896, 23.8], [81, 18.696], [81, -84.846], [75.896, -89.95], [58.75, -89.95], [45.25, -90.7], [37.813, -90.762], [27.583, -91.262], [24.33, -94.277]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[17.822, -99.613], [-14.75, -97.8], [-33.25, -96.3], [-48.75, -94.3], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 13.7], [-33.25, 11.7], [-16.75, 10.2], [7.955, 8.45], [12.275, 22.55], [23.75, 22.55], [40.25, 23.05], [57.75, 23.8], [75.896, 23.8], [81, 18.696], [81, -84.846], [75.896, -89.95], [58.75, -89.95], [45.25, -90.7], [37.813, -90.762], [27.583, -91.262], [24.33, -94.277]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}], "e": [{"i": [[3.164, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.819], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.819, 0], [0, 0], [0, 2.819], [0, 0], [2.819, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.819, 0], [0, 0], [0, -2.819], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.62, -3.102]], "v": [[10.759, -91.8], [-14.75, -91.8], [-33.25, -91.8], [-48.75, -91.8], [-75.896, -91.8], [-81, -86.696], [-81, 86.696], [-75.896, 91.8], [-64.504, 91.8], [-59.4, 86.696], [-59.4, 16.2], [-47.75, 16.2], [-33.25, 16.2], [-16.75, 16.2], [1.08, 16.2], [5.4, 37.8], [23.75, 37.8], [40.25, 37.8], [57.75, 37.8], [75.896, 37.8], [81, 32.696], [81, -65.096], [75.896, -70.2], [58.75, -70.2], [45.25, -70.2], [30.75, -70.2], [20.52, -70.2], [17.267, -86.465]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -74.262], [9.313, -68.699], [12.712, -65.912], [23.312, -65.912], [35.812, -65.912], [42.75, -65.85], [59.4, -65.85], [59.4, 4.7], [49.75, 4.7], [41.25, 4.7], [35.25, 4.7], [29.987, 4.7], [26.224, -6.614], [22.825, -9.4], [-6.25, -9.4], [-26.75, -9.4], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.2], [-18.75, -74.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -74.262], [9.313, -68.699], [12.712, -65.912], [23.312, -65.912], [35.812, -65.912], [42.75, -65.85], [59.4, -65.85], [59.4, 4.7], [49.75, 4.7], [41.25, 4.7], [35.25, 4.7], [29.987, 4.7], [26.224, -6.614], [22.825, -9.4], [-6.25, -9.4], [-26.75, -9.4], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.2], [-18.75, -74.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.474, -0.967], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.137, -79.95], [0.001, -79.824], [5.65, -79.678], [16.25, -79.678], [28.75, -80.928], [45.5, -83.428], [62.15, -85.928], [62.15, -19.863], [52.5, -19.863], [44, -17.363], [33.25, -15.613], [23.112, -13.613], [14.974, -13.208], [8.45, -15.15], [-6.25, -15.15], [-26.75, -12.9], [-43.75, -10.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -75.2], [-30.75, -77.7], [-18.75, -79.95]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.474, -0.967], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.137, -79.95], [0.001, -79.824], [5.65, -79.678], [16.25, -79.678], [28.75, -80.928], [45.5, -83.428], [62.15, -85.928], [62.15, -19.863], [52.5, -19.863], [44, -17.363], [33.25, -15.613], [23.112, -13.613], [14.974, -13.208], [8.45, -15.15], [-6.25, -15.15], [-26.75, -12.9], [-43.75, -10.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -75.2], [-30.75, -77.7], [-18.75, -79.95]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-4.474, -0.342], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.137, -57.012], [-1.999, -55.886], [6.9, -55.741], [15.75, -56.428], [27.5, -57.678], [45.25, -60.428], [62.15, -65.428], [61.65, -0.613], [55, 1.387], [43.5, 4.887], [34, 6.637], [23.862, 8.387], [13.224, 9.792], [-0.8, 9.85], [-15.75, 8.6], [-30.25, 5.85], [-46, 1.6], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -65.2], [-30.75, -60.2], [-18.75, -58.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-4.474, -0.342], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.137, -57.012], [-1.999, -55.886], [6.9, -55.741], [15.75, -56.428], [27.5, -57.678], [45.25, -60.428], [62.15, -65.428], [61.65, -0.613], [55, 1.387], [43.5, 4.887], [34, 6.637], [23.862, 8.387], [13.224, 9.792], [-0.8, 9.85], [-15.75, 8.6], [-30.25, 5.85], [-46, 1.6], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -65.2], [-30.75, -60.2], [-18.75, -58.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -78.012], [9.313, -72.449], [12.712, -69.662], [23.312, -69.662], [35.812, -69.162], [42.75, -69.1], [59.4, -68.35], [59.4, 2.2], [49.75, 1.45], [41.25, 1.45], [35.25, 1.45], [29.987, 0.95], [26.224, -10.364], [22.825, -13.15], [-6.25, -11.4], [-26.75, -9.9], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.7], [-18.75, -76.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -78.012], [9.313, -72.449], [12.712, -69.662], [23.312, -69.662], [35.812, -69.162], [42.75, -69.1], [59.4, -68.35], [59.4, 2.2], [49.75, 1.45], [41.25, 1.45], [35.25, 1.45], [29.987, 0.95], [26.224, -10.364], [22.825, -13.15], [-6.25, -11.4], [-26.75, -9.9], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.7], [-18.75, -76.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -74.262], [9.313, -68.699], [12.712, -65.912], [23.312, -65.912], [35.812, -65.912], [42.75, -65.85], [59.4, -65.85], [59.4, 4.7], [49.75, 4.7], [41.25, 4.7], [35.25, 4.7], [29.987, 4.7], [26.224, -6.614], [22.825, -9.4], [-6.25, -9.4], [-26.75, -9.4], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.2], [-18.75, -74.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -74.262], [9.313, -68.699], [12.712, -65.912], [23.312, -65.912], [35.812, -65.912], [42.75, -65.85], [59.4, -65.85], [59.4, 4.7], [49.75, 4.7], [41.25, 4.7], [35.25, 4.7], [29.987, 4.7], [26.224, -6.614], [22.825, -9.4], [-6.25, -9.4], [-26.75, -9.4], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.2], [-18.75, -74.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.474, -0.967], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.137, -79.95], [0.001, -79.824], [5.65, -79.678], [16.25, -79.678], [28.75, -80.928], [45.5, -83.428], [62.15, -85.928], [62.15, -19.863], [52.5, -19.863], [44, -17.363], [33.25, -15.613], [23.112, -13.613], [14.974, -13.208], [8.45, -15.15], [-6.25, -15.15], [-26.75, -12.9], [-43.75, -10.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -75.2], [-30.75, -77.7], [-18.75, -79.95]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.474, -0.967], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.137, -79.95], [0.001, -79.824], [5.65, -79.678], [16.25, -79.678], [28.75, -80.928], [45.5, -83.428], [62.15, -85.928], [62.15, -19.863], [52.5, -19.863], [44, -17.363], [33.25, -15.613], [23.112, -13.613], [14.974, -13.208], [8.45, -15.15], [-6.25, -15.15], [-26.75, -12.9], [-43.75, -10.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -75.2], [-30.75, -77.7], [-18.75, -79.95]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-4.474, -0.342], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.137, -57.012], [-1.999, -55.886], [6.9, -55.741], [15.75, -56.428], [27.5, -57.678], [45.25, -60.428], [62.15, -65.428], [61.65, -0.613], [55, 1.387], [43.5, 4.887], [34, 6.637], [23.862, 8.387], [13.224, 9.792], [-0.8, 9.85], [-15.75, 8.6], [-30.25, 5.85], [-46, 1.6], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -65.2], [-30.75, -60.2], [-18.75, -58.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.749, 0.023], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-4.474, -0.342], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.137, -57.012], [-1.999, -55.886], [6.9, -55.741], [15.75, -56.428], [27.5, -57.678], [45.25, -60.428], [62.15, -65.428], [61.65, -0.613], [55, 1.387], [43.5, 4.887], [34, 6.637], [23.862, 8.387], [13.224, 9.792], [-0.8, 9.85], [-15.75, 8.6], [-30.25, 5.85], [-46, 1.6], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -65.2], [-30.75, -60.2], [-18.75, -58.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -78.012], [9.313, -72.449], [12.712, -69.662], [23.312, -69.662], [35.812, -69.162], [42.75, -69.1], [59.4, -68.35], [59.4, 2.2], [49.75, 1.45], [41.25, 1.45], [35.25, 1.45], [29.987, 0.95], [26.224, -10.364], [22.825, -13.15], [-6.25, -11.4], [-26.75, -9.9], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.7], [-18.75, -76.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.551, -78.012], [9.313, -72.449], [12.712, -69.662], [23.312, -69.662], [35.812, -69.162], [42.75, -69.1], [59.4, -68.35], [59.4, 2.2], [49.75, 1.45], [41.25, 1.45], [35.25, 1.45], [29.987, 0.95], [26.224, -10.364], [22.825, -13.15], [-6.25, -11.4], [-26.75, -9.9], [-43.75, -7.9], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -72.7], [-30.75, -74.7], [-18.75, -76.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.653, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.324, 1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -1.62], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.512, -70.2], [2.251, -51.386], [5.65, -48.6], [16.25, -48.6], [28.75, -48.6], [42.75, -48.6], [59.4, -48.6], [59.4, 16.2], [49.75, 16.2], [41.25, 16.2], [33.25, 16.2], [23.112, 16.2], [19.349, -2.614], [15.95, -5.4], [-6.25, -5.4], [-26.75, -5.4], [-43.75, -5.4], [-59.4, -5.4], [-59.4, -70.2], [-45.75, -70.2], [-30.75, -70.2], [-18.75, -70.2]], "c": true}]}, {"t": 47}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.874509803922, 0.756862745098, 0.505882352941, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [81.25, 92.05], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 48, "st": 0, "bm": 0}], "markers": []}