<template>
  <v-card class="card-gradient-1 rounded-xl pa-2" elevation="6">
    <v-container fluid>
      <!-- game type tab -->
      <v-row no-gutters>
        <v-spacer></v-spacer>
        <v-col xl="8" lg="8" md="8" sm="8" cols="12" class="pl-4">
          <v-tabs
            v-model="selectedGameCategorySortNumber"
            color="primary"
            background-color="transparent"
            class="removeArrow"
          >
            <v-tab
              v-for="(category, index) in gameCategory"
              v-show="category.enable"
              :key="index"
              active-class="primary--text"
              class="v-tab-custom"
              :show-arrows="false"
              @click="selectedGameCategorySortNumber = index"
            >
              {{ $t(category.dict + '_game') }}
            </v-tab>
          </v-tabs>
        </v-col>
      </v-row>
      <!-- game title & slide group -->
      <v-row no-gutters class="align-top">
        <!-- game title -->
        <v-col cols="12" sm="4" md="4" lg="4">
          <v-card color="transparent" elevation="0">
            <!-- mdAndTop -->
            <!-- v-card-title 在使用漸層樣式會有問題所以用div 來代替 -->
            <template v-if="!$vuetify.breakpoint.xsOnly">
              <v-row no-gutters class="px-4">
                <span
                  class="gradient-title--text font-weight-bold"
                  :class="{
                    'custom-text-noto text-h2': $vuetify.breakpoint.mdAndUp,
                    'custom-h-5vw': $vuetify.breakpoint.smOnly
                  }"
                >
                  {{
                    selectedGameCategory.dict === 'slot'
                      ? 'CASINO'
                      : selectedGameCategory.dict === 'table'
                      ? 'BOARD'
                      : selectedGameCategory.dict === 'fishing'
                      ? 'FISHING'
                      : 'PARTY'
                  }}
                </span>
              </v-row>
              <v-row no-gutters class="px-4">
                <span
                  class="pt-1 text-left gradient-title--text font-weight-bold"
                  :class="{
                    'custom-text-noto text-h2': $vuetify.breakpoint.mdAndUp,
                    'custom-h-5vw': $vuetify.breakpoint.smOnly
                  }"
                >
                  GAMES
                </span>
              </v-row>
            </template>
            <v-card-text class="justify-left text-left pb-0 pl-4">
              <!-- smAndDown -->
              <v-row no-gutters>
                <span
                  v-if="$vuetify.breakpoint.xsOnly"
                  class="text-left gradient-title--text font-weight-bold"
                  style="font-size: 21px"
                >
                  {{
                    selectedGameCategory.dict === 'slot'
                      ? 'CASINO GAMES'
                      : selectedGameCategory.dict === 'table'
                      ? 'BOARD GAMES'
                      : selectedGameCategory.dict === 'fishing'
                      ? 'FISHING GAMES'
                      : 'PARTY GAMES'
                  }}
                </span>
              </v-row>
              <v-row
                no-gutters
                width="310px"
                :class="$vuetify.breakpoint.smAndDown ? 'mt-3' : ''"
                :style="{
                  height: $vuetify.breakpoint.smAndDown
                    ? showGameList.length > 2
                      ? '72px'
                      : '120px'
                    : $vuetify.breakpoint.mdOnly
                    ? '68px'
                    : '62px'
                }"
              >
                <span
                  class="font-weight-regular default-content--text custom-text-noto text-cation"
                >
                  {{
                    selectedGameCategory.dict === 'slot'
                      ? $t('hot_provider_description')
                      : selectedGameCategory.dict === 'table'
                      ? $t('hot_provider_description1')
                      : selectedGameCategory.dict === 'fishing'
                      ? $t('hot_provider_description2')
                      : $t('hot_provider_description3')
                  }}
                </span>
              </v-row>
              <v-expand-transition>
                <v-btn
                  v-if="showGameList.length > 2"
                  elevation="0"
                  color="gradient-button"
                  rounded
                  :class="{
                    'mt-2': $vuetify.breakpoint.lgAndUp,
                    'mt-6': $vuetify.breakpoint.mdOnly,
                    'mt-7': $vuetify.breakpoint.smOnly,
                    'mt-3': $vuetify.breakpoint.xsOnly
                  }"
                  :to="
                    localePath(
                      '/games?category=' + gameCategory[selectedGameCategorySortNumber].dict
                    )
                  "
                >
                  <span class="button-content--text custom-text-noto">
                    {{ $t('explore_all') }}
                  </span>
                </v-btn>
              </v-expand-transition>
            </v-card-text>
          </v-card>
        </v-col>
        <!-- slide group & arrow btn -->
        <v-col cols="12" sm="8" md="8" lg="8">
          <v-slide-group
            id="v-slide-custom"
            v-model="slideGroup"
            class="pa-4 pb-0"
            mandatory
            center-active
          >
            <v-slide-item
              v-for="(game, index) in showGameList"
              :key="index"
              v-show="showEnableStatus(game.enable)"
            >
              <gameCard
                :game-category-id="gameCategory[selectedGameCategorySortNumber].code"
                :index="index"
                :game="game"
                :show-main-daily-rtp="showMainDailyRtp"
                :style="{ width: $vuetify.breakpoint.xsOnly ? '150px' : '200px' }"
              />
            </v-slide-item>
          </v-slide-group>
          <v-row no-gutters v-if="showGameList.length === 0" align="center" style="height: 163px">
            <span class="grey-3--text pl-7" style="font-size: 20px">{{ $t('no_game') }}</span>
          </v-row>
          <!-- arrow btn -->
          <v-row
            class="pl-2 justify-space-between"
            no-gutters
            :class="{
              'pt-2': $vuetify.breakpoint.lgAndUp || $vuetify.breakpoint.xsOnly,
              'pt-4': $vuetify.breakpoint.mdOnly,
              'pt-6': showGameList.length > 2 && $vuetify.breakpoint.smOnly
            }"
          >
            <v-expand-transition>
              <div v-if="showGameList.length > 2">
                <v-btn color="gradient-button" fab dark x-small class="mx-2" @click="previousSlide">
                  <span class="material-symbols-rounded button-icon--text"> arrow_left </span>
                </v-btn>
                <v-btn color="gradient-button" fab dark x-small class="mx-2" @click="nextSlide">
                  <span class="material-symbols-rounded button-icon--text"> arrow_right </span>
                </v-btn>
              </div>
            </v-expand-transition>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
  </v-card>
</template>

<script>
  import gameList from '@/mixins/gameList'
  export default {
    mixins: [gameList],
    name: 'HotGames',
    components: {
      gameCard: () => import('~/components/game/gameCard.vue')
    },
    data() {
      return {
        selectedGameCategorySortNumber: 0,
        selectedGameCategory: 'slot',
        slideGroup: 0,
        showGameIframeStatus: false,
        showMainDailyRtp: false
      }
    },
    computed: {
      showGameList({ $store }) {
        return $store.getters['gameHall/hotGameList']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      gameCategory({ $store }) {
        return $store.getters['gameProvider/gameCategory']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      }
    },
    async created() {
      await this.$store.dispatch('gameProvider/fetch')
      if (this.gameCategory.length > 0) {
        const sortType = 2
        this.selectedGameCategory = this.gameCategory[0]
        await this.fetchGameList(this.selectedGameCategorySortNumber, sortType)
        this.showMainDailyRtp = true
        //一次顯示三個遊戲，起始值為1，呈現比較正常
        this.slideGroup = this.$vuetify.breakpoint.mdAndUp ? 1 : 0
      }
    },
    watch: {
      selectedGameCategorySortNumber: {
        async handler(sortNum, oldSortINum) {
          this.$store.dispatch('gameProvider/fetch')
          if (this.maintainSystem[0].maintaining) {
            return
          }
          const sortType = 2
          this.selectedGameCategory = this.gameCategory[sortNum]

          await this.fetchGameList(this.selectedGameCategorySortNumber, sortType)
          if (!this.gameCategory[sortNum].enable) {
            this.selectedGameCategorySortNumber = oldSortINum
          }
          this.showMainDailyRtp = true
          //一次顯示三個遊戲，起始值為1，呈現比較正常
          this.slideGroup = this.$vuetify.breakpoint.mdAndUp ? 1 : 0
        }
      }
    },
    mounted() {},
    methods: {
      nextSlide() {
        this.slideGroup += 1
      },
      previousSlide() {
        this.slideGroup -= 1
      },
      generateGameHallLink(gameCategory) {
        let url = ''
        url = '/game_list?category=' + gameCategory

        return this.localePath(url)
      },
      showMaintainNotify() {
        this.$store.dispatch('easySnackbar/setContent', {
          time: -1,
          type: 'warning',
          msg1: this.$t('maintenanceWithdrawalError'),
          msg2: this.$t('plz_wait_or_app'),
          show: true
        })
      },
      async fetchGameList(selectedGameCategorySortNumber, sortType) {
        if (this.maintainSystem[0].maintaining) {
          return
        }

        // 為避免點擊遊戲之後剛好掛維護，使用者進入遊戲時無法正常執行，故重新排序一次遊戲清單
        const gameCategoryId = this.gameCategory[selectedGameCategorySortNumber].code
        const lang = this.$i18n.locale
        const limit = 999
        await this.$store.dispatch('gameHall/fetchGameList', {
          gameCategoryId,
          sortType,
          lang,
          limit
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  #v-slide-custom ::v-deep {
    .v-slide-group__prev {
      display: none !important;
    }

    .v-slide-group__next {
      display: none !important;
    }
  }
  .removeArrow ::v-deep {
    .v-slide-group__prev {
      display: none !important;
    }

    .v-slide-group__next {
      display: none !important;
    }
  }
</style>
